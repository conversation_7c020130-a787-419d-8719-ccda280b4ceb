<template>
  <footer class="footer">
    <div class="container">
      <!-- 顶部服务特色区域 -->
      <div class="service-features">
        <div class="feature-item" style="justify-content: flex-start">
          <img src="@/assets/images/商城PC端4（无字）_134.jpg" alt="百强企业" class="feature-icon">
          <span class="feature-text">百强企业 品质保证</span>
        </div>
        <div class="feature-item">
          <img src="@/assets/images/c.jpg" alt="7天退换" class="feature-icon">
          <span class="feature-text">7天退换 15天换货</span>
        </div>
        <div class="feature-item">
          <img src="@/assets/images/商城PC端4（无字）_138.jpg" alt="满88包邮" class="feature-icon">
          <span class="feature-text">满88包邮</span>
        </div>
        <div class="feature-item" style="justify-content: flex-end">
          <img src="@/assets/images/商城PC端4（无字）_141.jpg" alt="官方售后" class="feature-icon">
          <span class="feature-text">官方售后全国联保</span>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="footer-content">
        <div class="footer-section">
          <div class="footer-title-wrapper" @click="toggleSection('products')">
            <h4 class="footer-title">产品分类</h4>
            <div class="toggle-icon" :class="{ 'expanded': expandedSections.products }">
              <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                <path d="M1 1L6 6L11 1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <transition name="slide-down">
            <ul class="footer-links" v-show="expandedSections.products">
              <li><a href="#">片刻闲系列</a></li>
              <li><a href="#">及时雨系列</a></li>
              <li><a href="#">盼秋叶系列</a></li>
              <li><a href="#">听风闲香系列</a></li>
              <li><a href="#">具象美物系列</a></li>
              <li><a href="#">三原系列</a></li>
              <li><a href="#">特色系列</a></li>
              <li><a href="#">人文旅系列</a></li>
            </ul>
          </transition>
        </div>

        <div class="footer-section">
          <div class="footer-title-wrapper" @click="toggleSection('service')">
            <h4 class="footer-title">服务中心</h4>
            <div class="toggle-icon" :class="{ 'expanded': expandedSections.service }">
              <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                <path d="M1 1L6 6L11 1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <transition name="slide-down">
            <ul class="footer-links" v-show="expandedSections.service">
              <li><a href="#">申请售后</a></li>
              <li><a href="#">订单查询</a></li>
              <li><a href="#">发票申请</a></li>
              <li><a href="#">友情链接</a></li>
              <li><a href="#">发票查询</a></li>
              <li><a href="#">客服中心</a></li>
            </ul>
          </transition>
        </div>

        <div class="footer-section">
          <div class="footer-title-wrapper" @click="toggleSection('stores')">
            <h4 class="footer-title">线下门店</h4>
            <div class="toggle-icon" :class="{ 'expanded': expandedSections.stores }">
              <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                <path d="M1 1L6 6L11 1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <transition name="slide-down">
            <ul class="footer-links" v-show="expandedSections.stores">
              <li><a href="#">梧州市六堡茶文化馆茶船古道旗舰店</a></li>
              <li><a href="#">梧州市居仁路茶船古道旗舰店</a></li>
              <li><a href="#">梧州市龙腾路茶船古道店</a></li>
              <li><a href="#">南宁市安园东路吉顺茶馆</a></li>
              <li><a href="#">南宁市凤岭南路1号梧州六堡茶馆</a></li>
              <li><a href="#">南宁市朱瑾路汇贤庄</a></li>
              <li><a href="#">岑溪市六堡茶店</a></li>
              <li><a href="#">南京市环湖假日店</a></li>
            </ul>
          </transition>
        </div>

        <div class="footer-section">
          <div class="footer-title-wrapper" @click="toggleSection('follow')">
            <h4 class="footer-title">关注我们</h4>
            <div class="toggle-icon" :class="{ 'expanded': expandedSections.follow }">
              <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                <path d="M1 1L6 6L11 1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <transition name="slide-down">
            <ul class="footer-links" v-show="expandedSections.follow">
              <li><a href="#">品牌官网</a></li>
              <li><a href="#">微信公众号</a></li>
              <li><a href="#">新浪微博</a></li>
              <li><a href="#">联系我们</a></li>
            </ul>
          </transition>
        </div>

        <div class="footer-section contact-section">
          <div class="footer-title-wrapper" @click="toggleSection('contact')">
            <h4 class="footer-title">联系我们</h4>
            <div class="toggle-icon" :class="{ 'expanded': expandedSections.contact }">
              <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                <path d="M1 1L6 6L11 1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
          </div>
          <transition name="slide-down">
            <div class="contact-info" v-show="expandedSections.contact">
              <div class="qr-code">
                <img src="@/assets/images/logo-only.png" alt="微信二维码" class="qr-image">
              </div>
              <div class="contact-details">
                <div class="hotline">
                  <span class="hotline-label">客服热线</span>
                  <span class="hotline-number">400-888-888</span>
                </div>
                <div class="other-contacts">
                  <img src="@/assets/images/footer/foot_logo1.png" alt="微信" class="contact-icon">
                  <img src="@/assets/images/footer/foot_logo2.png" alt="微博" class="contact-icon">
                  <img src="@/assets/images/footer/foot_logo3.png" alt="抖音" class="contact-icon">
                  <img src="@/assets/images/footer/foot_logo4.png" alt="小红书" class="contact-icon">
                  <img src="@/assets/images/footer/foot_logo5.png" alt="小红书" class="contact-icon">
                  <img src="@/assets/images/footer/foot_logo6.png" alt="小红书" class="contact-icon">
                  <img src="@/assets/images/footer/foot_logo7.png" alt="小红书" class="contact-icon">
                  <img src="@/assets/images/footer/foot_logo8.png" alt="小红书" class="contact-icon">
                  <img src="@/assets/images/footer/foot_logo9.png" alt="小红书" class="contact-icon">
                </div>
              </div>
            </div>
          </transition>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="footer-bottom">
        <p>版权所有广西梧州六堡茶股份有限公司   桂ICP备2025061531号</p>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 展开状态管理
const expandedSections = ref({
  products: true,
  service: true,
  stores: true,
  follow: true,
  contact: true
})

// 屏幕宽度监听
const screenWidth = ref(window.innerWidth)

// 切换区域展开/折叠状态
const toggleSection = (section) => {
  // 只在移动端启用折叠功能
  if (screenWidth.value <= 768) {
    expandedSections.value[section] = !expandedSections.value[section]
  }
}

// 根据屏幕尺寸设置初始状态
const updateExpandedState = () => {
  screenWidth.value = window.innerWidth

  if (screenWidth.value <= 768) {
    // 移动端默认折叠所有区域
    expandedSections.value = {
      products: false,
      service: false,
      stores: false,
      follow: false,
      contact: false
    }
  } else {
    // 桌面端默认展开所有区域
    expandedSections.value = {
      products: true,
      service: true,
      stores: true,
      follow: true,
      contact: true
    }
  }
}

// 窗口大小变化监听
const handleResize = () => {
  updateExpandedState()
}

onMounted(() => {
  updateExpandedState()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 页脚样式 */
.footer {
  background: #ffffff;
  color: #666;
  padding: 40px 0 20px;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 服务特色区域 */
.service-features {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 40px 0;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 50px;
  flex-wrap: wrap;
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 0 0 23%;
}

.feature-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.feature-text {
  font-size: 15px;
  color: #666;
  white-space: nowrap;
}

/* 主要内容区域 */
.footer-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 50px;
  gap: 30px;
}

.footer-section {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-width: 0;
}

/* 标题包装器 */
.footer-title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  user-select: none;
  width: 100%;
}

.footer-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  flex: 1;
}

/* 切换图标 */
.toggle-icon {
  display: none;
  width: 24px;
  height: 24px;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: transform 0.3s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.toggle-icon svg {
  width: 12px;
  height: 8px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: #666;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #333;
}

/* 过渡动画 */
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.slide-down-enter-from,
.slide-down-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.slide-down-enter-to,
.slide-down-leave-from {
  max-height: 500px;
  opacity: 1;
  transform: translateY(0);
}

/* 联系我们区域 */
.contact-section {
  align-items: flex-start;
}

.contact-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
}

.qr-code {
  text-align: center;
}

.qr-image {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.contact-details {
  text-align: center;
}

.hotline {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;
  margin-bottom: 15px;
}

.hotline-label {
  font-size: 12px;
  color: #999;
}

.hotline-number {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.other-contacts {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.contact-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.contact-icon:hover {
  opacity: 0.7;
}

/* 版权信息 */
.footer-bottom {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #e0e0e0;
  color: #999;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .container {
    max-width: 100%;
    padding: 0 30px;
  }

  .service-features {
    justify-content: center;
    flex-wrap: wrap;
    gap: 30px;
  }

  .feature-item {
    min-width: 180px;
  }

  .footer-content {
    flex-wrap: wrap;
    gap: 40px;
  }

  .footer-section {
    flex: 1 1 calc(50% - 20px);
    min-width: 250px;
  }

  .contact-section {
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 20px;
  }

  .footer {
    padding: 30px 0 20px;
  }

  .service-features {
    padding: 30px 0;
    margin-bottom: 40px;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 25px;
  }

  .feature-item {
    min-width: auto;
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .footer-content {
    flex-direction: column;
    gap: 20px;
    margin-bottom: 40px;
  }

  .footer-section {
    flex: none;
    width: 100%;
    text-align: left;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
  }

  .footer-section:last-child {
    border-bottom: none;
  }

  /* 移动端显示切换图标 */
  .toggle-icon {
    display: flex;
  }

  .footer-title-wrapper {
    border-bottom: none;
  }

  .footer-title-wrapper:hover {
    background-color: #f8f9fa;
  }

  .footer-title {
    font-size: 16px;
    margin-top: 0;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .footer-links {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 15px 0 0 0;
  }

  .footer-links li {
    margin-bottom: 0;
    width: 100%;
  }

  .footer-links a {
    font-size: 14px;
    display: block;
    padding: 8px 0;
  }

  .contact-info {
    align-items: flex-start;
    gap: 20px;
    padding: 15px 0 0 0;
  }

  .contact-details {
    text-align: left;
  }

  .hotline {
    align-items: flex-start;
    margin-bottom: 15px;
  }

  .other-contacts {
    justify-content: flex-start;
    gap: 12px;
  }

  .contact-icon {
    width: 24px;
    height: 24px;
  }

  .contact-section {
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 15px;
  }

  .footer {
    padding: 25px 0 15px;
  }

  .service-features {
    padding: 25px 0;
    margin-bottom: 30px;
    gap: 20px;
  }

  .feature-item {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .feature-icon {
    width: 36px;
    height: 36px;
  }

  .feature-text {
    font-size: 13px;
    white-space: normal;
    text-align: center;
  }

  .footer-content {
    gap: 15px;
    margin-bottom: 30px;
  }

  .footer-section {
    padding-bottom: 12px;
  }

  .footer-title-wrapper {
    padding: 0;
  }

  .footer-title {
    font-size: 15px;
    margin-top: 0;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .toggle-icon {
    width: 20px;
    height: 20px;
  }

  .toggle-icon svg {
    width: 10px;
    height: 6px;
  }

  .footer-links {
    gap: 8px;
    padding: 12px 0 0 0;
  }

  .footer-links a {
    font-size: 13px;
    padding: 6px 0;
  }

  .contact-info {
    padding: 12px 0 0 0;
    gap: 15px;
  }

  .qr-image {
    width: 60px;
    height: 60px;
  }

  .hotline-label {
    font-size: 12px;
  }

  .hotline-number {
    font-size: 16px;
  }

  .other-contacts {
    gap: 10px;
  }

  .contact-icon {
    width: 20px;
    height: 20px;
  }

  .footer-bottom {
    font-size: 12px;
    padding: 15px 0;
    line-height: 1.5;
  }

  .contact-section {
    justify-content: space-between;
  }
}
</style>