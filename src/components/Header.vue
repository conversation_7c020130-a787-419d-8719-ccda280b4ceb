<template>
  <header class="header" :class="[scroll ? 'header-main' : '']">
    <div class="nav-container" v-if="!isMobile">
      <div class="logo" @click="toHome">
        <img src="@/assets/logo-full.png" alt="六堡茶" class="logo-img">
      </div>

      <!-- 桌面端导航菜单 -->
      <div class="nav-main">
        <nav class="nav-menu desktop-nav">
          <span class="nav-link" @click="navTo('/')">商城首页</span>
          <span class="nav-link" @click="navTo('/category?name=原叶茶')">原叶茶</span>
          <span class="nav-link" @click="navTo('/category?name=香系列')">香系列</span>
          <span class="nav-link" @click="navTo('/category?name=袋泡茶')">袋泡茶</span>
          <span class="nav-link" @click="navTo('/customize')">茶叶定制</span>
          <span class="nav-link" @click="navTo('/investment')">招商加盟</span>
          <span class="nav-link" @click="navTo('/coop')">供链合作</span>
          <span class="nav-link" @click="navTo('/contact')">联系我们</span>
        </nav>
        <div class="flex justify-end items-center">
          <img alt="购物车" class="cursor-pointer" src="@/assets/images/购物车.png" @click="toCart"></img>
          <img alt="用户中心" class="cursor-pointer" src="@/assets/images/用户.png" @click="toUser"></img>
        </div>
      </div>
    </div>

    <!-- 移动端头部导航栏 -->
    <div v-if="isMobile" class="mobile-header">
      <div class="mobile-header-content">
        <div class="mobile-left">
          <img src="@/assets/menu.png" alt="菜单" class="menu-icon" @click="toggleMobileMenu">
          <img src="@/assets/mobile-logo.png" alt="六堡茶" class="mobile-logo">
        </div>
        <div class="mobile-right">
          <img alt="购物车" class="mobile-icon" src="@/assets/images/购物车.png" @click="toCart">
          <img alt="用户中心" class="mobile-icon" src="@/assets/images/用户.png" @click="toUser">
        </div>
      </div>
    </div>

    <!-- 移动端导航菜单（全屏覆盖） -->
    <div v-if="isMobile && showMobileMenu" class="mobile-nav-overlay" @click="toggleMobileMenu">
      <div class="mobile-nav-menu" @click.stop>
        <div class="mobile-nav-header">
          <span class="mobile-nav-title">导航</span>
          <button class="mobile-nav-close" @click="toggleMobileMenu">×</button>
        </div>
        <div class="mobile-nav-content">
          <span class="mobile-nav-link" @click="mobileNavTo('/')">
            首页
          </span>
          <span class="mobile-nav-link" @click="mobileNavTo('/category?name=原叶茶')">
            原叶茶
          </span>
          <span class="mobile-nav-link" @click="mobileNavTo('/category?name=香系列')">
            香系列
          </span>
          <span class="mobile-nav-link" @click="mobileNavTo('/category?name=袋泡茶')">
            袋泡茶
          </span>
          <span class="mobile-nav-link" @click="mobileNavTo('/customize')">
            茶叶定制
          </span>
          <span class="mobile-nav-link" @click="mobileNavTo('/investment')">
            招商加盟
          </span>
          <span class="mobile-nav-link" @click="mobileNavTo('/coop')">
            供链合作
          </span>
          <span class="mobile-nav-link" @click="mobileNavTo('/contact')">
            联系我们
          </span>
        </div>
      </div>
    </div>

  </header>
  <div style="height: 70px;" v-if="!isMobile"></div>
  <div style="height: 60px;" v-else></div>
  <div class="search-wrap">
    <div class="search-container">
      <div class="search-icon">
        <img src="@/assets/images/搜索.png" alt="搜索"/>
      </div>
      <input
        type="text"
        v-model="searchQuery"
        placeholder="搜索商品"
        class="search-input"
        @keyup.enter="handleSearch"
      />
      <button class="search-btn" @click="handleSearch">
        搜索
      </button>
    </div>
  </div>
</template>

<script setup>
import {ref} from 'vue'
import {useRouter} from "vue-router";

const showDropdown = ref(false)
const showMobileMenu = ref(false)
const showMobileDropdown = ref(false)
const searchQuery = ref('')
const isMobile = ref(false)
const deviceInfo = ref({
  type: '',
  os: '',
  browser: '',
  screenWidth: 0
})

// 下拉菜单延迟控制
let dropdownTimer = null

const router = useRouter()

const toUser = () => {
  router.push('/user')
}

const toCart = () => {
  router.push('/cart')
  window.scrollTo(0, 0)
}

// 获取设备信息
const getDeviceInfo = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  const screenWidth = window.innerWidth

  // 检测操作系统
  let os = 'Unknown'
  if (userAgent.includes('android')) os = 'Android'
  else if (userAgent.includes('iphone') || userAgent.includes('ipad')) os = 'iOS'
  else if (userAgent.includes('windows')) os = 'Windows'
  else if (userAgent.includes('mac')) os = 'macOS'
  else if (userAgent.includes('linux')) os = 'Linux'

  // 检测浏览器
  let browser = 'Unknown'
  if (userAgent.includes('chrome')) browser = 'Chrome'
  else if (userAgent.includes('firefox')) browser = 'Firefox'
  else if (userAgent.includes('safari')) browser = 'Safari'
  else if (userAgent.includes('edge')) browser = 'Edge'

  // 检测设备类型
  let type = 'Desktop'
  if (userAgent.includes('mobile') || screenWidth <= 768) type = 'Mobile'
  else if (userAgent.includes('tablet') || (screenWidth > 768 && screenWidth <= 1024)) type = 'Tablet'

  deviceInfo.value = {type, os, browser, screenWidth}

  return {type, os, browser, screenWidth}
}

// 检测是否为移动设备
const checkMobile = () => {
  const {type, screenWidth} = getDeviceInfo()
  const userAgent = navigator.userAgent.toLowerCase()

  // 更精确的移动设备检测
  const mobileKeywords = ['mobile', 'android', 'iphone', 'ipod', 'blackberry', 'windows phone']
  const tabletKeywords = ['ipad', 'tablet']

  const isMobileDevice = mobileKeywords.some(keyword => userAgent.includes(keyword))
  const isTabletDevice = tabletKeywords.some(keyword => userAgent.includes(keyword))
  const isSmallScreen = screenWidth <= 768
  const isMediumScreen = screenWidth > 768 && screenWidth <= 1024

  // 移动设备或小屏幕显示移动端界面
  // 平板设备根据屏幕宽度决定
  isMobile.value = isMobileDevice || isSmallScreen || (isTabletDevice && isMediumScreen)

}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
  // 如果切换到桌面端，关闭移动端菜单
  if (!isMobile.value) {
    showMobileMenu.value = false
    showMobileDropdown.value = false
  }
}

// 桌面端导航
const navTo = (path) => {
  router.push(path)
  window.scrollTo(0, 0)
}

// 移动端导航
const mobileNavTo = (path) => {
  showMobileMenu.value = false
  showMobileDropdown.value = false
  router.push(path)
  window.scrollTo(0, 0)
}

// 切换移动端菜单
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
  if (showMobileMenu.value) {
    showMobileDropdown.value = false
  }
}

// 切换移动端下拉菜单
const toggleMobileDropdown = () => {
  showMobileDropdown.value = !showMobileDropdown.value
}

// 搜索处理
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    console.log('搜索内容:', searchQuery.value)
    // 这里可以添加搜索逻辑，比如跳转到搜索结果页面
    // router.push(`/search?q=${encodeURIComponent(searchQuery.value)}`)

    // 或者触发搜索事件给父组件处理
    // emit('search', searchQuery.value)
  }
}

// PC端下拉菜单控制（带延迟）
const handleDropdownEnter = () => {
  if (!isMobile.value) {
    // 清除之前的隐藏定时器
    if (dropdownTimer) {
      clearTimeout(dropdownTimer)
      dropdownTimer = null
    }
    // 立即显示下拉菜单
    showDropdown.value = true
  }
}

const handleDropdownLeave = () => {
  if (!isMobile.value) {
    // 延迟隐藏下拉菜单，给用户时间移动鼠标
    dropdownTimer = setTimeout(() => {
      showDropdown.value = false
      dropdownTimer = null
    }, 200) // 200ms 延迟
  }
}

// 处理产品系列导航
const handleProductNavigation = (productType) => {
  // 关闭下拉菜单
  showDropdown.value = false
  if (dropdownTimer) {
    clearTimeout(dropdownTimer)
    dropdownTimer = null
  }

  // 这里可以根据产品类型导航到不同页面
  // 暂时导航到首页的产品区域
  router.push('/')

  // 滚动到产品区域（如果在首页）
  setTimeout(() => {
    const productSection = document.querySelector('.product-showcase')
    if (productSection) {
      productSection.scrollIntoView({behavior: 'smooth'})
    }
  }, 100)

  // 可以在这里添加产品筛选逻辑
  console.log('选择的产品系列:', productType)
}

// 处理触摸事件（移动端优化）
const handleTouchStart = (e) => {
  if (isMobile.value) {
    // 记录触摸开始位置
    const touch = e.touches[0]
    window.touchStartY = touch.clientY
  }
}

const handleTouchEnd = (e) => {
  if (isMobile.value && window.touchStartY) {
    const touch = e.changedTouches[0]
    const touchEndY = touch.clientY
    const deltaY = touchEndY - window.touchStartY

    // 向上滑动关闭菜单
    if (deltaY < -50 && showMobileMenu.value) {
      showMobileMenu.value = false
      showMobileDropdown.value = false
    }
  }
}

// 防止移动端菜单滚动时页面滚动
const preventBodyScroll = (prevent) => {
  if (prevent) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
}

// 监听移动端菜单状态变化
import {watch} from 'vue'

watch(showMobileMenu, (newVal) => {
  if (isMobile.value) {
    preventBodyScroll(newVal)
  }
})

// 组件挂载时初始化
import {onMounted, onUnmounted} from 'vue'

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)

  // 添加触摸事件监听（移动端优化）
  if (isMobile.value) {
    document.addEventListener('touchstart', handleTouchStart, {passive: true})
    document.addEventListener('touchend', handleTouchEnd, {passive: true})
  }

  // 点击外部关闭菜单
  document.addEventListener('click', (e) => {
    const header = document.querySelector('.header')
    if (header && !header.contains(e.target)) {
      showMobileMenu.value = false
      showMobileDropdown.value = false
      showDropdown.value = false
    }
  })

  // ESC键关闭菜单
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      showMobileMenu.value = false
      showMobileDropdown.value = false
      showDropdown.value = false
    }
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('touchstart', handleTouchStart)
  document.removeEventListener('touchend', handleTouchEnd)
  preventBodyScroll(false) // 确保页面滚动恢复正常

  // 清理下拉菜单定时器
  if (dropdownTimer) {
    clearTimeout(dropdownTimer)
    dropdownTimer = null
  }
})

const props = defineProps({
  scroll: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: Boolean,
    default: false
  }
})

const toHome = () => {
  window.location.href = 'http://liubao-tea.glyhy.com'
}

// 暴露给父组件的方法和数据
defineExpose({
  isMobile,
  deviceInfo,
  checkMobile,
  getDeviceInfo
})
</script>

<style scoped>
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  color: #333;
}

.header-main {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 70px;

  .nav-main {
    margin-left: 20px;
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;

    img {
      width: 1.25rem;
      height: 1.25rem;
      margin: 0 20px;
    }
  }
}

.logo {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
  padding-top: 25px;
  cursor: pointer;
}

.logo-img {
  width: 160px;
  object-fit: cover;
}


.nav-menu {
  display: flex;
  gap: 20px;
}

.nav-link {
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
  padding: 5px 0;
  cursor: pointer;
}

.nav-link:hover,
.nav-link.active {
  color: #961D13;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 2px;
  background: #961D13;
}

.nav-right {
  display: flex;
  align-items: center;
}

.dropdown-container {
  position: relative;
  outline: none;
}

.dropdown-container:focus {
  color: #961D13;
  box-shadow: 0 0 0 2px rgba(150, 29, 19, 0.3);
  border-radius: 4px;
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 8px;
  border-radius: 8px;
  background: rgba(15, 38, 46, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: white;
  font-size: 14px;
  min-width: 120px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
}

.dropdown::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid rgba(15, 38, 46, 0.95);
}

.dropdown .item {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dropdown .item:hover {
  color: #961D13;
  background: rgba(255, 255, 255, 0.1);
}

.dropdown .item:last-child {
  border-bottom: none;
}

.dropdown .item:first-child {
  border-radius: 8px 8px 0 0;
}

.dropdown .item:last-child {
  border-radius: 0 0 8px 8px;
}

.dropdown .item:focus {
  outline: none;
  color: #961D13;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 0 2px rgba(150, 29, 19, 0.5);
}

/* 下拉菜单过渡动画 */
.dropdown-fade-enter-active,
.dropdown-fade-leave-active {
  transition: all 0.3s ease;
}

.dropdown-fade-enter-from {
  opacity: 0;
  transform: translateX(-50%) translateY(-10px);
}

.dropdown-fade-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(-10px);
}

.search-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 20px;
  max-width: 1200px;
  margin: auto;
  background-color: white;
}

.search-container {
  display: flex;
  align-items: center;
  max-width: 500px;
  width: 100%;
  background: #f8f9fa;
  border-radius: 25px;
  border: 1px solid #e0e0e0;
  overflow: hidden;
  position: relative;
}

.search-icon {
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.search-icon img {
  width: 18px;
  height: 18px;
  opacity: 0.5;
}

.search-input {
  flex: 1;
  padding: 12px 0;
  border: none;
  outline: none;
  font-size: 14px;
  background: transparent;
  color: #333;
  min-width: 0;
}

.search-input::placeholder {
  color: #999;
  font-size: 14px;
}

.search-btn {
  padding: 12px 20px;
  background: #52a58a;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.3s ease;
  border-radius: 0 25px 25px 0;
  margin: -1px -1px -1px 0;
  flex-shrink: 0;
  white-space: nowrap;
}

.search-btn:hover {
  background: #459a7e;
}

.search-btn:active {
  background: #3e8a70;
}

/* 移动端头部导航栏 */
.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: white;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mobile-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 15px;
}

.mobile-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.menu-icon {
  width: 28px;
  height: 28px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.menu-icon:hover {
  transform: scale(1.1);
}

.mobile-logo {
  width: 100px;
  height: auto;
  object-fit: contain;
}

.mobile-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.mobile-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.mobile-icon:hover {
  transform: scale(1.1);
}

/* 移动端导航菜单覆盖层 */
.mobile-nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 移动端导航菜单 */
.mobile-nav-menu {
  width: 280px;
  height: 100vh;
  background: white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  transform: translateX(-100%);
  animation: slideIn 0.3s ease forwards;
  overflow-y: auto;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.mobile-nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #52a58a 0%, #459a7e 100%);
  color: white;
}

.mobile-nav-title {
  font-size: 18px;
  font-weight: 600;
}

.mobile-nav-close {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.mobile-nav-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.mobile-nav-content {
  padding: 20px 0;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #333;
  font-size: 16px;
  font-weight: 500;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid #f0f0f0;
}

.mobile-nav-link:hover {
  background: #f8f9fa;
  color: #52a58a;
  padding-left: 30px;
}

.nav-icon {
  font-size: 18px;
  width: 20px;
  text-align: center;
}

.mobile-dropdown {
  margin-top: 10px;
  padding-left: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mobile-dropdown-item {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  padding: 10px 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobile-dropdown-item:hover {
  color: #961D13;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .logo-img {
    width: 120px;
    margin-left: 10px;
  }

  .nav-container {
    padding: 0 15px;
  }

  .mobile-logo {
    width: 90px;
  }

  .mobile-nav-menu {
    width: 260px;
  }

  .mobile-nav-link {
    font-size: 15px;
    padding: 12px 20px;
  }

  .nav-icon {
    font-size: 16px;
  }

  .search-wrap {
    padding: 12px 15px;
  }

  .search-container {
    max-width: 100%;
    border-radius: 20px;
  }

  .search-icon {
    padding: 0 12px;
  }

  .search-icon img {
    width: 16px;
    height: 16px;
  }

  .search-input {
    font-size: 13px;
    padding: 10px 0;
  }

  .search-btn {
    font-size: 13px;
    padding: 10px 16px;
    border-radius: 0 20px 20px 0;
  }
}

@media (max-width: 480px) {
  .logo-img {
    width: 100px;
    margin-left: 10px;
  }

  .mobile-header-content {
    padding: 0 12px;
  }

  .mobile-left {
    gap: 10px;
  }

  .menu-icon {
    width: 26px;
    height: 26px;
  }

  .mobile-logo {
    width: 80px;
  }

  .mobile-right {
    gap: 12px;
  }

  .mobile-icon {
    width: 22px;
    height: 22px;
  }

  .mobile-nav-menu {
    width: 240px;
  }

  .mobile-nav-header {
    padding: 15px;
  }

  .mobile-nav-title {
    font-size: 16px;
  }

  .mobile-nav-close {
    font-size: 22px;
  }

  .mobile-nav-content {
    padding: 15px 0;
  }

  .mobile-nav-link {
    font-size: 14px;
    padding: 12px 15px;
  }

  .mobile-nav-link:hover {
    padding-left: 25px;
  }

  .nav-icon {
    font-size: 15px;
    width: 18px;
  }

  .nav-container {
    padding: 0 10px;
  }

  .search-wrap {
    padding: 10px 12px;
  }

  .search-container {
    border-radius: 18px;
  }

  .search-icon {
    padding: 0 10px;
  }

  .search-icon img {
    width: 14px;
    height: 14px;
  }

  .search-input {
    font-size: 13px;
    padding: 9px 0;
  }

  .search-btn {
    font-size: 12px;
    padding: 9px 14px;
    border-radius: 0 18px 18px 0;
  }
}

/* 开发环境设备信息显示 */
.dev-info {
  position: fixed;
  top: 70px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  z-index: 10000;
  font-family: 'Courier New', monospace;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.dev-info-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.dev-info-content span {
  padding: 2px 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
}

.dev-info-content .mobile {
  background: #e74c3c;
  color: white;
}

.dev-info-content .desktop {
  background: #27ae60;
  color: white;
}

/* 移动端隐藏开发信息 */
@media (max-width: 480px) {
  .dev-info {
    display: none;
  }
}
</style>
