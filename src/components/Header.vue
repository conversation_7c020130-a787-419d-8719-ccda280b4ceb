<template>
  <header class="header" :class="[scroll ? 'header-main' : '']">
    <div class="nav-container" v-if="!isMobile">
      <div class="logo">
        <img src="@/assets/logo-full.png" alt="六堡茶" class="logo-img">
      </div>

      <!-- 桌面端导航菜单 -->
      <div class="nav-main">
        <nav class="nav-menu desktop-nav">
          <span class="nav-link" @click="navTo('/')">商城首页</span>
          <span class="nav-link" @click="navTo('/category?name=原叶茶')">原叶茶</span>
          <span class="nav-link" @click="navTo('/category?name=香系列')">香系列</span>
          <span class="nav-link" @click="navTo('/category?name=袋泡茶')">袋泡茶</span>
          <span class="nav-link" @click="navTo('/customize')">茶叶定制</span>
          <span class="nav-link" @click="navTo('/investment')">招商加盟</span>
          <span class="nav-link" @click="navTo('/coop')">供链合作</span>
          <span class="nav-link" @click="navTo('/contact')">联系我们</span>
        </nav>
        <div class="flex justify-end items-center">
          <img alt="购物车" class="cursor-pointer" src="@/assets/images/购物车.png"></img>
          <img alt="用户中心" class="cursor-pointer" src="@/assets/images/用户.png" @click="toUser"></img>
        </div>
      </div>
    </div>

    <!-- 移动端导航菜单 -->
    <div v-if="isMobile" class="mobile-nav" :class="{ active: showMobileMenu }">
      <div class="mobile-nav-content" v-if="showMobileMenu">
        <span class="mobile-nav-link" @click="mobileNavTo('/')">首页</span>
        <span class="mobile-nav-link" @click="mobileNavTo('/about')">关于我们</span>
        <span class="mobile-nav-link" @click="mobileNavTo('/activity')">品牌动态</span>
        <div class="mobile-nav-link" @click="toggleMobileDropdown">
          <span>产品系列</span>
          <div class="mobile-dropdown" v-if="showMobileDropdown">
            <span class="mobile-dropdown-item">原叶茶</span>
            <span class="mobile-dropdown-item">香系列</span>
            <span class="mobile-dropdown-item">袋泡茶</span>
          </div>
        </div>
        <span class="mobile-nav-link" @click="mobileNavTo('/customize')">茶叶定制</span>
        <span class="mobile-nav-link" @click="mobileNavTo('/dangJian')">党建工作</span>
        <span class="mobile-nav-link" @click="mobileNavTo('/investment')">招商加盟</span>
        <span class="mobile-nav-link" @click="mobileNavTo('/coop')">供链合作</span>
        <span class="mobile-nav-link" @click="mobileNavTo('/contact')">联系我们</span>
      </div>
      <!-- 移动端菜单按钮 -->
      <div class="flex justify-between item-center">
        <div class="logo">
          <img src="@/assets/logo-full.png" alt="六堡茶" class="logo-img">
        </div>
        <div class="mobile-menu-btn" @click="toggleMobileMenu">
          <img src="@/assets/menu.png" alt="" style="width: 30px">
        </div>
      </div>
    </div>

  </header>
  <div style="height: 70px;"></div>
  <div class="search-wrap">
    <div class="search-container">
      <div class="search-icon">
        <img src="@/assets/images/搜索.png" alt="搜索" />
      </div>
      <input
        type="text"
        v-model="searchQuery"
        placeholder=""
        class="search-input"
        @keyup.enter="handleSearch"
      />
    </div>
    <button class="search-btn" @click="handleSearch">
      搜索
    </button>
  </div>
</template>

<script setup>
import {ref} from 'vue'
import {useRouter} from "vue-router";

const showDropdown = ref(false)
const showMobileMenu = ref(false)
const showMobileDropdown = ref(false)
const isMobile = ref(false)
const searchQuery = ref('')
const deviceInfo = ref({
  type: '',
  os: '',
  browser: '',
  screenWidth: 0
})

// 下拉菜单延迟控制
let dropdownTimer = null

const router = useRouter()

const toUser = () => {
  router.push('/user')
}

// 获取设备信息
const getDeviceInfo = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  const screenWidth = window.innerWidth

  // 检测操作系统
  let os = 'Unknown'
  if (userAgent.includes('android')) os = 'Android'
  else if (userAgent.includes('iphone') || userAgent.includes('ipad')) os = 'iOS'
  else if (userAgent.includes('windows')) os = 'Windows'
  else if (userAgent.includes('mac')) os = 'macOS'
  else if (userAgent.includes('linux')) os = 'Linux'

  // 检测浏览器
  let browser = 'Unknown'
  if (userAgent.includes('chrome')) browser = 'Chrome'
  else if (userAgent.includes('firefox')) browser = 'Firefox'
  else if (userAgent.includes('safari')) browser = 'Safari'
  else if (userAgent.includes('edge')) browser = 'Edge'

  // 检测设备类型
  let type = 'Desktop'
  if (userAgent.includes('mobile') || screenWidth <= 768) type = 'Mobile'
  else if (userAgent.includes('tablet') || (screenWidth > 768 && screenWidth <= 1024)) type = 'Tablet'

  deviceInfo.value = {type, os, browser, screenWidth}

  return {type, os, browser, screenWidth}
}

// 检测是否为移动设备
const checkMobile = () => {
  const {type, screenWidth} = getDeviceInfo()
  const userAgent = navigator.userAgent.toLowerCase()

  // 更精确的移动设备检测
  const mobileKeywords = ['mobile', 'android', 'iphone', 'ipod', 'blackberry', 'windows phone']
  const tabletKeywords = ['ipad', 'tablet']

  const isMobileDevice = mobileKeywords.some(keyword => userAgent.includes(keyword))
  const isTabletDevice = tabletKeywords.some(keyword => userAgent.includes(keyword))
  const isSmallScreen = screenWidth <= 768
  const isMediumScreen = screenWidth > 768 && screenWidth <= 1024

  // 移动设备或小屏幕显示移动端界面
  // 平板设备根据屏幕宽度决定
  isMobile.value = isMobileDevice || isSmallScreen || (isTabletDevice && isMediumScreen)

  // 开发环境下输出设备信息
  if (process.env.NODE_ENV === 'development') {
    console.log('设备信息:', deviceInfo.value)
    console.log('是否移动端:', isMobile.value)
  }
}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
  // 如果切换到桌面端，关闭移动端菜单
  if (!isMobile.value) {
    showMobileMenu.value = false
    showMobileDropdown.value = false
  }
}

// 桌面端导航
const navTo = (path) => {
  router.push(path)
  window.scrollTo(0, 0)
}

// 移动端导航
const mobileNavTo = (path) => {
  showMobileMenu.value = false
  showMobileDropdown.value = false
  router.push(path)
  window.scrollTo(0, 0)
}

// 切换移动端菜单
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
  if (showMobileMenu.value) {
    showMobileDropdown.value = false
  }
}

// 切换移动端下拉菜单
const toggleMobileDropdown = () => {
  showMobileDropdown.value = !showMobileDropdown.value
}

// 搜索处理
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    console.log('搜索内容:', searchQuery.value)
    // 这里可以添加搜索逻辑，比如跳转到搜索结果页面
    // router.push(`/search?q=${encodeURIComponent(searchQuery.value)}`)

    // 或者触发搜索事件给父组件处理
    // emit('search', searchQuery.value)
  }
}

// PC端下拉菜单控制（带延迟）
const handleDropdownEnter = () => {
  if (!isMobile.value) {
    // 清除之前的隐藏定时器
    if (dropdownTimer) {
      clearTimeout(dropdownTimer)
      dropdownTimer = null
    }
    // 立即显示下拉菜单
    showDropdown.value = true
  }
}

const handleDropdownLeave = () => {
  if (!isMobile.value) {
    // 延迟隐藏下拉菜单，给用户时间移动鼠标
    dropdownTimer = setTimeout(() => {
      showDropdown.value = false
      dropdownTimer = null
    }, 200) // 200ms 延迟
  }
}

// 处理产品系列导航
const handleProductNavigation = (productType) => {
  // 关闭下拉菜单
  showDropdown.value = false
  if (dropdownTimer) {
    clearTimeout(dropdownTimer)
    dropdownTimer = null
  }

  // 这里可以根据产品类型导航到不同页面
  // 暂时导航到首页的产品区域
  router.push('/')

  // 滚动到产品区域（如果在首页）
  setTimeout(() => {
    const productSection = document.querySelector('.product-showcase')
    if (productSection) {
      productSection.scrollIntoView({behavior: 'smooth'})
    }
  }, 100)

  // 可以在这里添加产品筛选逻辑
  console.log('选择的产品系列:', productType)
}

// 处理触摸事件（移动端优化）
const handleTouchStart = (e) => {
  if (isMobile.value) {
    // 记录触摸开始位置
    const touch = e.touches[0]
    window.touchStartY = touch.clientY
  }
}

const handleTouchEnd = (e) => {
  if (isMobile.value && window.touchStartY) {
    const touch = e.changedTouches[0]
    const touchEndY = touch.clientY
    const deltaY = touchEndY - window.touchStartY

    // 向上滑动关闭菜单
    if (deltaY < -50 && showMobileMenu.value) {
      showMobileMenu.value = false
      showMobileDropdown.value = false
    }
  }
}

// 防止移动端菜单滚动时页面滚动
const preventBodyScroll = (prevent) => {
  if (prevent) {
    document.body.style.overflow = 'hidden'
  } else {
    document.body.style.overflow = ''
  }
}

// 监听移动端菜单状态变化
import {watch} from 'vue'

watch(showMobileMenu, (newVal) => {
  if (isMobile.value) {
    preventBodyScroll(newVal)
  }
})

// 组件挂载时初始化
import {onMounted, onUnmounted} from 'vue'

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)

  // 添加触摸事件监听（移动端优化）
  if (isMobile.value) {
    document.addEventListener('touchstart', handleTouchStart, {passive: true})
    document.addEventListener('touchend', handleTouchEnd, {passive: true})
  }

  // 点击外部关闭菜单
  document.addEventListener('click', (e) => {
    const header = document.querySelector('.header')
    if (header && !header.contains(e.target)) {
      showMobileMenu.value = false
      showMobileDropdown.value = false
      showDropdown.value = false
    }
  })

  // ESC键关闭菜单
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      showMobileMenu.value = false
      showMobileDropdown.value = false
      showDropdown.value = false
    }
  })
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('touchstart', handleTouchStart)
  document.removeEventListener('touchend', handleTouchEnd)
  preventBodyScroll(false) // 确保页面滚动恢复正常

  // 清理下拉菜单定时器
  if (dropdownTimer) {
    clearTimeout(dropdownTimer)
    dropdownTimer = null
  }
})

const props = defineProps({
  scroll: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: Boolean,
    default: false
  }
})

// 暴露给父组件的方法和数据
defineExpose({
  isMobile,
  deviceInfo,
  checkMobile,
  getDeviceInfo
})
</script>

<style scoped>
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  color: #333;
}

.header-main{
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 70px;

  .nav-main {
    margin-left: 20px;
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;

    img {
      width: 1.25rem;
      height: 1.25rem;
      margin: 0 20px;
    }
  }
}

.logo {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
  padding-top: 25px;
}

.logo-img {
  width: 160px;
  object-fit: cover;
}


.nav-menu {
  display: flex;
  gap: 20px;
}

.nav-link {
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.3s ease;
  position: relative;
  padding: 5px 0;
  cursor: pointer;
}

.nav-link:hover,
.nav-link.active {
  color: #961D13;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 2px;
  background: #961D13;
}

.nav-right {
  display: flex;
  align-items: center;
}

.dropdown-container {
  position: relative;
  outline: none;
}

.dropdown-container:focus {
  color: #961D13;
  box-shadow: 0 0 0 2px rgba(150, 29, 19, 0.3);
  border-radius: 4px;
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 8px;
  border-radius: 8px;
  background: rgba(15, 38, 46, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  color: white;
  font-size: 14px;
  min-width: 120px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
}

.dropdown::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid rgba(15, 38, 46, 0.95);
}

.dropdown .item {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dropdown .item:hover {
  color: #961D13;
  background: rgba(255, 255, 255, 0.1);
}

.dropdown .item:last-child {
  border-bottom: none;
}

.dropdown .item:first-child {
  border-radius: 8px 8px 0 0;
}

.dropdown .item:last-child {
  border-radius: 0 0 8px 8px;
}

.dropdown .item:focus {
  outline: none;
  color: #961D13;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: inset 0 0 0 2px rgba(150, 29, 19, 0.5);
}

/* 下拉菜单过渡动画 */
.dropdown-fade-enter-active,
.dropdown-fade-leave-active {
  transition: all 0.3s ease;
}

.dropdown-fade-enter-from {
  opacity: 0;
  transform: translateX(-50%) translateY(-10px);
}

.dropdown-fade-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(-10px);
}

.search-wrap{
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 20px;
  max-width: 1200px;
  margin: auto;
}

.search-container {
  display: flex;
  align-items: center;
  max-width: 500px;
  width: 100%;
  background: white;
  border-radius: 30px;
  border: 1px solid #1c705e;
  overflow: hidden;
  position: relative;
}

.search-icon {
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon img {
  width: 20px;
  height: 20px;
  opacity: 0.6;
}

.search-input {
  flex: 1;
  padding: 15px 0;
  border: none;
  outline: none;
  font-size: 16px;
  background: transparent;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.search-btn {
  padding: 12px 30px;
  background: #1c705e;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.3s ease;
  border-radius: 25px;
  margin: 3px 10px;
  width: 200px;
}

.search-btn:hover {
  background: #459a7e;
}

.search-btn:active {
  background: #3e8a70;
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  padding-top: 20px;
  height: 100%;
}

/* 移动端导航菜单 */
.mobile-nav {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  background: rgba(15, 38, 46, 0.98);
  backdrop-filter: blur(10px);
  transform: translateY(-100%);
  transition: transform 0.3s ease-in-out;
  z-index: 999;
  max-height: calc(100vh - 70px);
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.mobile-nav.active {
  transform: translateY(0);
  top: 0;
}

.mobile-nav-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mobile-nav-link {
  color: white;
  font-size: 18px;
  font-weight: 500;
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobile-nav-link:hover {
  color: #961D13;
  padding-left: 10px;
}

.mobile-dropdown {
  margin-top: 10px;
  padding-left: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mobile-dropdown-item {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  padding: 10px 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobile-dropdown-item:hover {
  color: #961D13;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .logo-img {
    width: 120px;
  }

  .nav-container {
    padding: 0 15px;
  }

  .search-wrap {
    padding: 15px 20px;
  }

  .search-container {
    max-width: 100%;
  }

  .search-icon {
    padding: 0 15px;
  }

  .search-icon img {
    width: 18px;
    height: 18px;
  }

  .search-input {
    font-size: 14px;
    padding: 12px 0;
  }

  .search-btn {
    font-size: 14px;
    padding: 10px 20px;
  }
}

@media (max-width: 480px) {
  .logo-img {
    width: 100px;
  }

  .mobile-nav-link {
    font-size: 16px;
    padding: 12px 0;
  }

  .mobile-dropdown-item {
    font-size: 14px;
    padding: 8px 0;
  }

  .nav-container {
    padding: 0 10px;
  }

  .search-wrap {
    padding: 10px 15px;
  }

  .search-icon {
    padding: 0 12px;
  }

  .search-icon img {
    width: 16px;
    height: 16px;
  }

  .search-input {
    font-size: 14px;
    padding: 10px 0;
  }

  .search-btn {
    font-size: 14px;
    padding: 8px 16px;
  }
}

/* 开发环境设备信息显示 */
.dev-info {
  position: fixed;
  top: 70px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  z-index: 10000;
  font-family: 'Courier New', monospace;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.dev-info-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.dev-info-content span {
  padding: 2px 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.1);
}

.dev-info-content .mobile {
  background: #e74c3c;
  color: white;
}

.dev-info-content .desktop {
  background: #27ae60;
  color: white;
}

/* 移动端隐藏开发信息 */
@media (max-width: 480px) {
  .dev-info {
    display: none;
  }
}
</style>
