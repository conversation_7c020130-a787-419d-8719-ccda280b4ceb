<template>
  <div class="bottom-navbar">
    <div class="navbar-container">
      <div 
        class="navbar-item" 
        v-for="(item, index) in navItems" 
        :key="index"
        :class="{ active: activeIndex === index }"
        @click="handleNavClick(index, item)"
      >
        <div class="navbar-icon">
          <img :src="getIconSrc(item, activeIndex === index)" :alt="item.label" />
        </div>
        <span class="navbar-label">{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'

// 导入图标
import homeIcon from '@/assets/navbar/home.png'
import homeActiveIcon from '@/assets/navbar/home-active.png'
import categoryIcon from '@/assets/navbar/cate.png'
import categoryActiveIcon from '@/assets/navbar/cate-active.png'
import cartIcon from '@/assets/images/购物车.png'
import cartActiveIcon from '@/assets/images/cart-green.png'
import serviceIcon from '@/assets/navbar/service.png'
import serviceActiveIcon from '@/assets/navbar/service-active.png'
import profileIcon from '@/assets/images/用户.png'
import profileActiveIcon from '@/assets/navbar/user-active.png'

const props = defineProps({
  isMobile: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const route = useRoute()

// 导航项配置
const navItems = ref([
  {
    label: '首页',
    path: '/',
    icon: homeIcon,
    activeIcon: homeActiveIcon
  },
  {
    label: '分类',
    path: '/category',
    icon: categoryIcon,
    activeIcon: categoryActiveIcon
  },
  {
    label: '购物车',
    path: '/cart',
    icon: cartIcon,
    activeIcon: cartActiveIcon
  },
  {
    label: '服务',
    path: '/',
    icon: serviceIcon,
    activeIcon: serviceActiveIcon
  },
  {
    label: '我的',
    path: '/user',
    icon: profileIcon,
    activeIcon: profileActiveIcon
  }
])

// 当前激活的导航项索引
const activeIndex = computed(() => {
  const currentPath = route.path
  const index = navItems.value.findIndex(item => {
    if (item.path === '/') {
      return currentPath === '/' || currentPath === '/home'
    }
    return currentPath.startsWith(item.path)
  })
  return index >= 0 ? index : 0
})

// 获取图标源
const getIconSrc = (item, isActive) => {
  return isActive ? item.activeIcon : item.icon
}

// 处理导航点击
const handleNavClick = (index, item) => {
  if (route.path !== item.path) {
    // 滚动到顶部
    window.scrollTo(0, 0)
    router.push(item.path)
  }
}
</script>

<style scoped>
.bottom-navbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #ffffff;
  border-top: 1px solid #e5e5e5;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  padding-bottom: env(safe-area-inset-bottom);
}

.navbar-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 8px 0 12px;
  max-width: 100%;
}

.navbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 50px;
}

.navbar-item:active {
  transform: scale(0.95);
}

.navbar-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.navbar-label {
  font-size: 12px;
  color: #666666;
  font-weight: 400;
  line-height: 1;
  text-align: center;
  transition: color 0.3s ease;
}

.navbar-item.active .navbar-label {
  color: #1c705e;
  font-weight: 500;
}

/* 适配不同屏幕尺寸 */
@media (max-width: 375px) {
  .navbar-icon {
    width: 22px;
    height: 22px;
  }
  
  .navbar-label {
    font-size: 11px;
  }
}

@media (min-width: 414px) {
  .navbar-icon {
    width: 26px;
    height: 26px;
  }
  
  .navbar-label {
    font-size: 13px;
  }
}

/* 为底部导航栏预留空间 */
.navbar-spacer {
  height: 70px;
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
