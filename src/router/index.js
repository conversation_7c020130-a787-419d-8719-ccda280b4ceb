import * as VueRouter from "vue-router";
import Home from '@/views/Home.vue'
import Category from "@/views/Category.vue";

const routes = [
    {
        path: '/',
        name: 'home',
        component: Home
    },
    {
        path: '/category',
        name: 'Category',
        component: Category
    },
]

const router = VueRouter.createRouter({
    history: VueRouter.createWebHashHistory(),
    routes,
})

export default router