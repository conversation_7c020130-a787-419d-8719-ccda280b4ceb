import * as VueRouter from "vue-router";
import Home from '@/views/Home.vue'
import Category from "@/views/Category.vue";
import ProductDetail from "@/views/ProductDetail.vue";
import UserCenter from "@/views/UserCenter.vue";

const routes = [
    {
        path: '/',
        name: 'home',
        component: Home
    },
    {
        path: '/category',
        name: 'Category',
        component: Category
    },
    {
        path: '/detail',
        name: 'ProductDetail',
        component: ProductDetail
    },
    {
        path: '/user',
        name: 'UserCenter',
        component: UserCenter
    },
]

const router = VueRouter.createRouter({
    history: VueRouter.createWebHashHistory(),
    routes,
})

export default router