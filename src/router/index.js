import * as VueRouter from "vue-router";
import Home from '@/views/Home.vue'
import Category from "@/views/Category.vue";
import ProductDetail from "@/views/ProductDetail.vue";
import UserCenter from "@/views/UserCenter.vue";
import Cart from "@/views/Cart.vue";
import Coupon from "@/views/Coupon.vue";
import OrderList from "@/views/OrderList.vue";
import Address from "@/views/Address.vue";
import Contact from "@/views/Contact.vue";
import Investment from "@/views/Investment.vue";
import Coop from "@/views/Coop.vue";
import Customize from "@/views/Customize.vue";

const routes = [
    {
        path: '/',
        name: 'home',
        component: Home
    },
    {
        path: '/category',
        name: 'Category',
        component: Category
    },
    {
        path: '/detail',
        name: 'ProductDetail',
        component: ProductDetail
    },
    {
        path: '/user',
        name: 'UserCenter',
        component: UserCenter
    },
    {
        path: '/cart',
        name: 'Cart',
        component: Cart
    },
    {
        path: '/coupon',
        name: 'Coupon',
        component: Coupon
    },
    {
        path: '/orders',
        name: 'OrderList',
        component: OrderList
    },
    {
        path: '/contact',
        name: 'contact',
        component: Contact
    },
    {
        path: '/investment',
        name: 'investment',
        component: Investment
    },
    {
        path: '/coop',
        name: 'coop',
        component: Coop
    },
    {
        path: '/customize',
        name: 'customize',
        component: Customize
    },
]

const router = VueRouter.createRouter({
    history: VueRouter.createWebHashHistory(),
    routes,
})

export default router