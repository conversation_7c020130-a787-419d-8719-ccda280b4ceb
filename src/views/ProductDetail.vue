<template>
  <div class="product-detail-page">
    <!-- 产品展示区域 -->
    <div class="product-showcase container">
      <div class="product-gallery">
        <div class="main-image">
          <img :src="product.mainImage" :alt="product.title" />
        </div>
        <div class="thumbnail-slider">
          <div class="slider-arrow left" @click="prevImage">
            <i class="el-icon-arrow-left"></i>
          </div>
          <div class="thumbnails">
            <div 
              v-for="(image, index) in product.images" 
              :key="index" 
              class="thumbnail" 
              :class="{ active: currentImageIndex === index }"
              @click="selectImage(index)"
            >
              <img :src="image" :alt="`${product.title} - 图片${index + 1}`" />
            </div>
          </div>
          <div class="slider-arrow right" @click="nextImage">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
      
      <div class="product-info">
        <h1 class="product-title">{{ product.title }}</h1>
        
        <div class="product-price-section">
          <div class="price-label">价格</div>
          <div class="price-value">¥{{ product.price }}</div>
        </div>
        
        <div class="product-meta-section">
          <div class="meta-item">
            <span class="meta-label">运费</span>
            <span class="meta-value">{{ product.shipping }}</span>
            <span class="meta-extra">*支持快递配送</span>
          </div>
        </div>
        
        <div class="product-options-section">
          <div class="option-item">
            <div class="option-label">数量</div>
            <div class="quantity-selector">
              <el-input-number v-model="quantity" :min="1" :max="99" size="small"></el-input-number>
              <span class="stock-info">(库存{{ product.stock }}件)</span>
            </div>
          </div>
          
          <div class="option-item">
            <div class="option-label">配送方式</div>
            <div class="delivery-selector">
              <el-select v-model="deliveryMethod" placeholder="请选择配送方式">
                <el-option label="快递配送" value="express"></el-option>
                <el-option label="门店自提" value="pickup"></el-option>
              </el-select>
            </div>
          </div>
          
          <div class="option-item">
            <div class="option-label">规格</div>
            <div class="spec-selector">
              <el-radio-group v-model="selectedSpec">
                <el-radio v-for="(spec, index) in product.specs" :key="index" :label="spec">{{ spec }}</el-radio>
              </el-radio-group>
            </div>
          </div>
          
          <div class="option-item">
            <div class="option-label">服务</div>
            <div class="service-tags">
              <span class="service-tag" v-for="(service, index) in product.services" :key="index">
                <i class="el-icon-check"></i> {{ service }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="product-actions">
          <el-button type="primary" class="buy-now-btn">立即购买</el-button>
          <el-button type="default" class="add-to-cart-btn">加入购物车</el-button>
        </div>
      </div>
    </div>
    
    <!-- 产品详情选项卡 -->
    <div class="product-tabs container">
      <div class="tabs-header">
        <div 
          class="tab-item" 
          :class="{ active: activeTab === 'detail' }"
          @click="activeTab = 'detail'"
        >
          产品详情
        </div>
        <div 
          class="tab-item" 
          :class="{ active: activeTab === 'params' }"
          @click="activeTab = 'params'"
        >
          产品参数
        </div>
      </div>
      
      <div class="tabs-content">
        <!-- 产品详情内容 -->
        <div v-if="activeTab === 'detail'" class="tab-content detail-content">
          <div class="product-description">
            <h3>产品介绍</h3>
            <p>{{ product.description }}</p>
          </div>
          
          <div class="product-images">
            <img 
              v-for="(image, index) in product.detailImages" 
              :key="index" 
              :src="image" 
              :alt="`${product.title} - 详情图${index + 1}`" 
              class="detail-image"
            />
          </div>
        </div>
        
        <!-- 产品参数内容 -->
        <div v-if="activeTab === 'params'" class="tab-content params-content">
          <table class="params-table">
            <tbody>
              <tr v-for="(param, index) in product.params" :key="index">
                <td class="param-name">{{ param.name }}</td>
                <td class="param-value">{{ param.value }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <!-- 相关推荐 -->
    <div class="related-products container">
      <h3 class="section-title">相关推荐</h3>
      <div class="related-grid">
        <div class="related-item" v-for="(item, index) in relatedProducts" :key="index">
          <div class="related-image">
            <img :src="item.image" :alt="item.title" />
          </div>
          <div class="related-title">{{ item.title }}</div>
          <div class="related-price">¥{{ item.price }}</div>
        </div>
      </div>
    </div>
    
    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-sections">
          <div class="footer-section">
            <h4>产品中心</h4>
            <ul>
              <li>小青柑</li>
              <li>铁观音</li>
              <li>大红袍</li>
              <li>普洱熟茶</li>
              <li>武夷岩茶</li>
              <li>龙井</li>
              <li>碧螺春</li>
              <li>金骏眉</li>
              <li>茉莉花茶</li>
              <li>茶具套装</li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>服务中心</h4>
            <ul>
              <li>购物指南</li>
              <li>支付方式</li>
              <li>配送方式</li>
              <li>售后服务</li>
              <li>服务条款</li>
              <li>常见问题</li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>关于我们</h4>
            <ul>
              <li>公司简介</li>
              <li>企业文化</li>
              <li>发展历程</li>
              <li>荣誉资质</li>
              <li>招商加盟</li>
              <li>联系我们</li>
              <li>隐私政策</li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>联系方式</h4>
            <ul>
              <li>客服电话：***********</li>
              <li>公司地址：中国茶叶之都</li>
              <li>营业时间：9:00-18:00</li>
              <li>电子邮箱：<EMAIL></li>
            </ul>
          </div>
        </div>
        
        <div class="footer-bottom">
          <div class="footer-links">
            <a href="#" class="footer-link">
              <i class="el-icon-star-off"></i>
              <span>收藏本站</span>
            </a>
            <a href="#" class="footer-link">
              <i class="el-icon-phone"></i>
              <span>在线客服</span>
            </a>
            <a href="#" class="footer-link">
              <i class="el-icon-message"></i>
              <span>官方微信</span>
            </a>
          </div>
          
          <div class="qrcode">
            <img src="https://picsum.photos/100/100?random=200" alt="官方微信二维码"/>
          </div>
          
          <div class="company-info">
            <div class="company-logo">
              <img src="https://picsum.photos/100/50?random=201" alt="公司logo"/>
            </div>
            <div class="contact-info">
              <div>***********</div>
              <div class="social-icons">
                <img src="https://picsum.photos/20/20?random=202" alt="微博"/>
                <img src="https://picsum.photos/20/20?random=203" alt="微信"/>
                <img src="https://picsum.photos/20/20?random=204" alt="抖音"/>
                <img src="https://picsum.photos/20/20?random=205" alt="京东"/>
                <img src="https://picsum.photos/20/20?random=206" alt="淘宝"/>
              </div>
            </div>
          </div>
        </div>
        
        <div class="copyright">
          <p>版权所有 © 2023 茶叶商城 | 备案号：粤ICP备12345678号 | 网站地图</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 产品数据
const product = ref({
  id: 1,
  title: '京韵古道 悦鉴兰香青柑六罐装 香韵醇',
  price: '58',
  shipping: '¥10元起(满88元包邮)',
  stock: 999,
  mainImage: 'https://picsum.photos/600/600?random=1',
  images: [
    'https://picsum.photos/100/100?random=1',
    'https://picsum.photos/100/100?random=2',
    'https://picsum.photos/100/100?random=3'
  ],
  specs: ['礼盒装（上品）', '礼盒装（精品）', '罐装（标准装）'],
  services: ['正品保证', '7天无理由退货', '48小时发货', '顺丰配送'],
  description: '京韵古道悦鉴兰香青柑，精选优质新会小青柑，搭配云南大叶种晒青毛茶，经过传统工艺精制而成。茶香浓郁，口感醇厚，回甘持久，具有理气化痰、消食化滞的功效。',
  detailImages: [
    'https://picsum.photos/800/600?random=10',
    'https://picsum.photos/800/600?random=11',
    'https://picsum.photos/800/600?random=12'
  ],
  params: [
    { name: '品牌', value: '京韵古道' },
    { name: '产地', value: '广东新会' },
    { name: '净含量', value: '30g*6罐' },
    { name: '保质期', value: '36个月' },
    { name: '存储方法', value: '密封、干燥、避光、防异味' },
    { name: '生产日期', value: '见包装' },
    { name: '配料表', value: '新会小青柑、云南大叶种晒青毛茶' },
    { name: '食品添加剂', value: '无' },
    { name: '产品标准号', value: 'GB/T 19598' }
  ]
})

// 相关推荐产品
const relatedProducts = ref([
  { title: '小青柑礼盒装', price: '128', image: 'https://picsum.photos/200/200?random=20' },
  { title: '陈皮普洱茶', price: '98', image: 'https://picsum.photos/200/200?random=21' },
  { title: '茶具套装', price: '368', image: 'https://picsum.photos/200/200?random=22' }
])

// 当前选中的图片索引
const currentImageIndex = ref(0)

// 选择图片
const selectImage = (index) => {
  currentImageIndex.value = index
  product.value.mainImage = product.value.images[index]
}

// 上一张图片
const prevImage = () => {
  if (currentImageIndex.value > 0) {
    selectImage(currentImageIndex.value - 1)
  } else {
    selectImage(product.value.images.length - 1)
  }
}

// 下一张图片
const nextImage = () => {
  if (currentImageIndex.value < product.value.images.length - 1) {
    selectImage(currentImageIndex.value + 1)
  } else {
    selectImage(0)
  }
}

// 购买数量
const quantity = ref(1)

// 配送方式
const deliveryMethod = ref('express')

// 选中的规格
const selectedSpec = ref('礼盒装（上品）')

// 当前激活的选项卡
const activeTab = ref('detail')
</script>

<style scoped>
.product-detail-page {
  width: 100%;
}

/* 产品展示区域 */
.product-showcase {
  display: flex;
  margin: 30px auto;
  gap: 40px;
}

.product-gallery {
  width: 45%;
}

.main-image {
  width: 100%;
  height: 400px;
  border: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.main-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.thumbnail-slider {
  display: flex;
  align-items: center;
}

.slider-arrow {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  cursor: pointer;
}

.thumbnails {
  display: flex;
  gap: 10px;
  flex: 1;
  overflow-x: auto;
  padding: 0 10px;
}

.thumbnail {
  width: 80px;
  height: 80px;
  border: 1px solid #ddd;
  padding: 2px;
  cursor: pointer;
}

.thumbnail.active {
  border-color: #1e8e3e;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  flex: 1;
}

.product-title {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
}

.product-price-section {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  background-color: #f9f9f9;
  padding: 15px;
}

.price-label {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
}

.price-value {
  font-size: 28px;
  color: #e53935;
  font-weight: bold;
}

.product-meta-section {
  margin-bottom: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.meta-label {
  width: 60px;
  color: #666;
}

.meta-value {
  color: #333;
}

.meta-extra {
  margin-left: 10px;
  color: #999;
  font-size: 12px;
}

.product-options-section {
  margin-bottom: 30px;
}

.option-item {
  display: flex;
  margin-bottom: 20px;
}

.option-label {
  width: 60px;
  color: #666;
  line-height: 32px;
}

.quantity-selector {
  display: flex;
  align-items: center;
}

.stock-info {
  margin-left: 10px;
  color: #999;
  font-size: 12px;
}

.spec-selector {
  flex: 1;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.service-tag {
  display: inline-flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 5px 10px;
  border-radius: 3px;
  font-size: 12px;
  color: #666;
}

.service-tag i {
  color: #1e8e3e;
  margin-right: 5px;
}

.product-actions {
  display: flex;
  gap: 20px;
}

.buy-now-btn {
  background-color: #e53935;
  border-color: #e53935;
}

.add-to-cart-btn {
  background-color: #fff;
  color: #e53935;
  border-color: #e53935;
}

/* 产品详情选项卡 */
.product-tabs {
  margin: 40px auto;
}

.tabs-header {
  display: flex;
  border-bottom: 1px solid #eee;
}

.tab-item {
  padding: 15px 30px;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #1e8e3e;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #1e8e3e;
}

.tabs-content {
  padding: 30px 0;
}

.product-description {
  margin-bottom: 30px;
}

.product-description h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 15px;
}

.product-description p {
  line-height: 1.8;
  color: #666;
}

.product-images {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-image {
  width: 100%;
  height: auto;
}

.params-table {
  width: 100%;
  border-collapse: collapse;
}

.params-table tr {
  border-bottom: 1px solid #eee;
}

.params-table td {
  padding: 12px;
}

.param-name {
  width: 120px;
  color: #666;
  background-color: #f9f9f9;
}

.param-value {
  color: #333;
}

/* 相关推荐 */
.related-products {
  margin: 40px auto;
}

.section-title {
  font-size: 20px;
  color: #333;
  margin-bottom: 20px;
  position: relative;
  padding-left: 15px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: #1e8e3e;
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.related-item {
  border: 1px solid #eee;
  padding: 10px;
  transition: all 0.3s;
}

.related-item:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.related-image {
  width: 100%;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.related-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.related-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.related-price {
  color: #e53935;
  font-weight: bold;
}

/* 页脚样式 */
.footer {
  background-color: #f5f5f5;
  padding: 40px 0 20px;
  margin-top: 60px;
}

.footer-sections {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  margin-bottom: 30px;
}

.footer-section h4 {
  font-size: 16px;
  color: #333;
  margin-bottom: 15px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
}

.footer-section li:hover {
  color: #1e8e3e;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.footer-links {
  display: flex;
}

.footer-link {
  display: flex;
  align-items: center;
  margin-right: 20px;
  color: #666;
  text-decoration: none;
}

.footer-link i {
  margin-right: 5px;
}

.qrcode {
  text-align: center;
}

.company-info {
  display: flex;
  align-items: center;
}

.company-logo {
  margin-right: 15px;
}

.social-icons {
  display: flex;
  gap: 10px;
  margin-top: 5px;
}

.social-icons img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.copyright {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin-top: 20px;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .product-showcase {
    flex-direction: column;
  }
  
  .product-gallery {
    width: 100%;
  }
  
  .related-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .footer-sections {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .footer-bottom {
    flex-direction: column;
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .related-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-sections {
    grid-template-columns: 1fr;
  }
  
  .product-actions {
    flex-direction: column;
  }
}
</style>