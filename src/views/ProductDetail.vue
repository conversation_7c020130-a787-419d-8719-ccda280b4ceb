<template>
  <div class="product-detail-page">
    <Header :scroll="scroll"/>
    <!-- 产品展示区域 -->
    <div class="product-showcase container">
      <div class="product-gallery">
        <div class="main-image">
          <img :src="product.mainImage" :alt="product.title"/>
        </div>
        <div class="thumbnail-slider">
          <div class="slider-arrow left" @click="prevImage">
            <el-icon>
              <ArrowLeft/>
            </el-icon>
          </div>
          <div class="thumbnails">
            <div
              v-for="(image, index) in product.images"
              :key="index"
              class="thumbnail"
              :class="{ active: currentImageIndex === index }"
              @click="selectImage(index)"
            >
              <img :src="image" :alt="`${product.title} - 图片${index + 1}`"/>
            </div>
          </div>
          <div class="slider-arrow right" @click="nextImage">
            <el-icon>
              <ArrowRight/>
            </el-icon>
          </div>
        </div>
      </div>

      <div class="product-info">
        <h1 class="product-title">{{ product.title }}</h1>

        <div class="price-row">
          <span class="price-label">价格</span>
          <span class="price-value">¥{{ product.price }}</span>
        </div>

        <div class="activity-row">
          <span class="activity-label">活动</span>
          <span class="activity-text">618活动全城8折</span>
        </div>

        <div class="spec-row">
          <span class="spec-label">规格</span>
          <div class="spec-options">
            <button
              v-for="(spec, index) in product.specs"
              :key="index"
              class="spec-option"
              :class="{ active: selectedSpec === spec }"
              @click="selectedSpec = spec"
            >
              {{ spec }}
            </button>
          </div>
        </div>

        <div class="delivery-row">
          <span class="delivery-label">配送方式</span>
          <div class="delivery-options">
            <button
              class="delivery-option"
              :class="{ active: deliveryMethod === 'express' }"
              @click="deliveryMethod = 'express'"
            >
              快递寄送
            </button>
            <button
              class="delivery-option"
              :class="{ active: deliveryMethod === 'pickup' }"
              @click="deliveryMethod = 'pickup'"
            >
              门店自提
            </button>
          </div>
        </div>

        <div class="shipping-row">
          <span class="shipping-label">发货</span>
          <span class="shipping-info">配送至：上海 | 普通快递：7元 | 满88元包邮</span>
        </div>

        <div class="quantity-row">
          <span class="quantity-label">数量</span>
          <div class="quantity-controls">
            <button class="quantity-btn" @click="decreaseQuantity">-</button>
            <input type="number" v-model="quantity" class="quantity-input" min="1" max="99">
            <button class="quantity-btn" @click="increaseQuantity">+</button>
          </div>
        </div>

        <div class="action-buttons">
          <button class="buy-now-btn">立即购买</button>
          <button class="add-cart-btn" @click="toCart">加入购物车</button>
        </div>
      </div>
    </div>

    <!-- 推荐商品区域 - 桌面端侧边栏 -->
    <div class="flex justify-start items-start">
      <div class="recommended-products desktop-recommended">
        <h3 class="recommended-title">推荐商品</h3>

        <div class="recommended-arrow up" @click="scrollRecommendedUp">
          <svg width="20" height="12" viewBox="0 0 20 12" fill="none">
            <path d="M10 0L20 12H0L10 0Z" fill="#999"/>
          </svg>
        </div>

        <div class="recommended-list" ref="recommendedList">
          <div class="recommended-item" v-for="(item, index) in recommendedProducts" :key="index">
            <div class="recommended-image">
              <img :src="item.image" :alt="item.title"/>
            </div>
            <div class="recommended-info">
              <div class="recommended-item-title">{{ item.title }}</div>
            </div>
          </div>
        </div>

        <div class="recommended-arrow down" @click="scrollRecommendedDown">
          <svg width="20" height="12" viewBox="0 0 20 12" fill="none">
            <path d="M10 12L0 0H20L10 12Z" fill="#999"/>
          </svg>
        </div>
      </div>

      <!-- 产品参数区域 -->
      <div class="flex flex-col justify-start items-start w-full">
        <!-- 移动端推荐商品区域 -->
        <div class="mobile-recommended-section">
          <h3 class="mobile-recommended-title">推荐商品</h3>
          <div class="mobile-recommended-grid">
            <div class="mobile-recommended-item" v-for="(item, index) in recommendedProducts" :key="index">
              <div class="mobile-recommended-image">
                <img :src="item.image" :alt="item.title"/>
              </div>
              <div class="mobile-recommended-info">
                <div class="mobile-recommended-item-title">{{ item.title }}</div>
                <div v-if="item.badge" class="mobile-recommended-badge">{{ item.badge }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="product-params-section container">
          <h3 class="params-section-title">产品参数</h3>
          <div class="params-grid">
            <div class="params-row">
              <div class="param-item">
                <span class="param-label">品牌：</span>
                <span class="param-value">茶船古道</span>
              </div>
              <div class="param-item">
                <span class="param-label">原料：</span>
                <span class="param-value">六堡毛茶</span>
              </div>
              <div class="param-item">
                <span class="param-label">汤色：</span>
                <span class="param-value">茶汤红亮</span>
              </div>
              <div class="param-item">
                <span class="param-label">外形：</span>
                <span class="param-value">干茶条索紧结均匀，黑褐尚润</span>
              </div>
            </div>

            <div class="params-row">
              <div class="param-item">
                <span class="param-label">品名：</span>
                <span class="param-value">茶船古道·锦绣</span>
              </div>
              <div class="param-item">
                <span class="param-label">形状：</span>
                <span class="param-value">散茶</span>
              </div>
              <div class="param-item">
                <span class="param-label">陈化日期：</span>
                <span class="param-value">2018年</span>
              </div>
              <div class="param-item">
                <span class="param-label">保质期：</span>
                <span class="param-value">可长期存放，且越陈越香</span>
              </div>
            </div>

            <div class="params-row">
              <div class="param-item">
                <span class="param-label">净重：</span>
                <span class="param-value">60g（4g*15袋）</span>
              </div>
              <div class="param-item">
                <span class="param-label">等级：</span>
                <span class="param-value">一级</span>
              </div>
              <div class="param-item">
                <span class="param-label">口感：</span>
                <span class="param-value">滋味协调甘甜，甘醇顺滑</span>
              </div>
              <div class="param-item">
                <span class="param-label">保存方式：</span>
                <span class="param-value">通风、阴凉、干燥</span>
              </div>
            </div>
          </div>
        </div>

        <div class="product-params-section container">
          <h3 class="params-section-title">产品详情</h3>
          <img :src="productImg" :alt="product.title" style="width: 100%;"/>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <Footer/>
  </div>
</template>

<script setup>
import {ref} from 'vue'
import Header from "@/components/Header.vue";
import Footer from "@/components/Footer.vue";

const scroll = ref(false)
window.addEventListener('scroll', () => {
  // 顶部
  scroll.value = window.scrollY > 0
})

// 产品数据
import productImg from '@/assets/images/products/rw_img2.png'
import productImg1 from '@/assets/images/products/rw_img1.png'
import productImg2 from '@/assets/images/products/rw_img3.png'
import {ArrowLeft, ArrowRight} from "@element-plus/icons-vue";
import {useRouter} from "vue-router";

const product = ref({
  id: 1,
  title: '茶船古道·祝雪兰云雾六堡茶·春岚韵',
  price: '58',
  shipping: '¥10元起(满88元包邮)',
  stock: 999,
  mainImage: productImg,
  images: [
    productImg, productImg1, productImg2
  ],
  specs: ['尝新1盒装', '4盒送精致礼袋装', '一箱囤货装（6盒）'],
  services: ['正品保证', '7天无理由退货', '48小时发货', '顺丰配送'],
  description: '京韵古道悦鉴兰香青柑，精选优质新会小青柑，搭配云南大叶种晒青毛茶，经过传统工艺精制而成。茶香浓郁，口感醇厚，回甘持久，具有理气化痰、消食化滞的功效。',
  detailImages: [
    'https://picsum.photos/800/600?random=10',
    'https://picsum.photos/800/600?random=11',
    'https://picsum.photos/800/600?random=12'
  ],
  params: [
    {name: '品牌', value: '京韵古道'},
    {name: '产地', value: '广东新会'},
    {name: '净含量', value: '30g*6罐'},
    {name: '保质期', value: '36个月'},
    {name: '存储方法', value: '密封、干燥、避光、防异味'},
    {name: '生产日期', value: '见包装'},
    {name: '配料表', value: '新会小青柑、云南大叶种晒青毛茶'},
    {name: '食品添加剂', value: '无'},
    {name: '产品标准号', value: 'GB/T 19598'}
  ]
})

// 推荐商品数据
const recommendedProducts = ref([
  {
    title: '非遗六堡茶',
    image: productImg1,
    badge: ''
  },
  {
    title: '非遗六堡茶',
    image: productImg,
    badge: '-17108乡村振兴版-'
  },
  {
    title: '非遗六堡茶',
    image: productImg2,
    badge: ''
  }
])

// 相关推荐产品
const relatedProducts = ref([
  {title: '小青柑礼盒装', price: '128', image: 'https://picsum.photos/200/200?random=20'},
  {title: '陈皮普洱茶', price: '98', image: 'https://picsum.photos/200/200?random=21'},
  {title: '茶具套装', price: '368', image: 'https://picsum.photos/200/200?random=22'}
])

// 当前选中的图片索引
const currentImageIndex = ref(0)

// 选择图片
const selectImage = (index) => {
  currentImageIndex.value = index
  product.value.mainImage = product.value.images[index]
}

// 上一张图片
const prevImage = () => {
  if (currentImageIndex.value > 0) {
    selectImage(currentImageIndex.value - 1)
  } else {
    selectImage(product.value.images.length - 1)
  }
}

// 下一张图片
const nextImage = () => {
  if (currentImageIndex.value < product.value.images.length - 1) {
    selectImage(currentImageIndex.value + 1)
  } else {
    selectImage(0)
  }
}

// 购买数量
const quantity = ref(1)

// 配送方式
const deliveryMethod = ref('express')

// 选中的规格
const selectedSpec = ref('尝新1盒装')

// 当前激活的选项卡
const activeTab = ref('detail')

// 数量控制方法
const increaseQuantity = () => {
  if (quantity.value < 99) {
    quantity.value++
  }
}

const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

// 推荐商品滚动方法
const recommendedList = ref(null)

const scrollRecommendedUp = () => {
  if (recommendedList.value) {
    recommendedList.value.scrollBy({
      top: -200,
      behavior: 'smooth'
    })
  }
}

const scrollRecommendedDown = () => {
  if (recommendedList.value) {
    recommendedList.value.scrollBy({
      top: 200,
      behavior: 'smooth'
    })
  }
}

const router = useRouter()
const toCart = () => {
  router.push('/cart')
}
</script>

<style scoped>
.product-detail-page {
  max-width: 1200px;
  margin: auto;
  background-color: white;
  width: 100%;
}

/* 产品展示区域 */
.product-showcase {
  display: flex;
  margin: 30px auto;
  gap: 40px;
  padding: 0 20px;
}

.product-gallery {
  width: 45%;
  min-width: 0;
}

.main-image {
  width: 100%;
  max-width: 540px;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-slider {
  display: flex;
  align-items: center;
}

.slider-arrow {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  cursor: pointer;
}

.thumbnails {
  display: flex;
  gap: 10px;
  flex: 1;
  overflow-x: auto;
  padding: 0 10px;
  overflow-y: hidden;
}

.thumbnail {
  width: 80px;
  height: 80px;
  border: 1px solid #ddd;
  padding: 2px;
  cursor: pointer;
}

.thumbnail.active {
  border-color: #1e8e3e;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  flex: 1;
}

.product-title {
  font-size: 20px;
  color: #333;
  margin-bottom: 30px;
  line-height: 1.4;
}

/* 价格行 */
.price-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.price-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.price-value {
  font-size: 32px;
  color: #ff6600;
  font-weight: bold;
}

/* 活动行 */
.activity-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.activity-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.activity-text {
  font-size: 14px;
  color: #333;
}

/* 规格行 */
.spec-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
}

.spec-label {
  width: 80px;
  font-size: 14px;
  color: #666;
  line-height: 36px;
}

.spec-options {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.spec-option {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.spec-option.active {
  border-color: #52a58a;
  color: #52a58a;
}

.spec-option:hover {
  border-color: #52a58a;
}

/* 配送方式行 */
.delivery-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.delivery-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.delivery-options {
  display: flex;
  gap: 10px;
}

.delivery-option {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.delivery-option.active {
  border-color: #52a58a;
  color: #52a58a;
}

.delivery-option:hover {
  border-color: #52a58a;
}

/* 发货行 */
.shipping-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.shipping-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.shipping-info {
  font-size: 14px;
  color: #333;
}

/* 数量行 */
.quantity-row {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.quantity-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
}

.quantity-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: #f5f5f5;
  color: #333;
  font-size: 18px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.quantity-btn:hover {
  background: #e0e0e0;
}

.quantity-input {
  width: 60px;
  height: 36px;
  border: none;
  text-align: center;
  font-size: 14px;
  outline: none;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 15px;
}

.buy-now-btn {
  padding: 12px 30px;
  background: #1c705e;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.buy-now-btn:hover {
  background: #459a7e;
}

.add-cart-btn {
  padding: 12px 30px;
  background: #999;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.add-cart-btn:hover {
  background: #777;
}

/* 推荐商品区域 - 桌面端 */
.desktop-recommended {
  width: 200px;
  border-radius: 8px;
  padding: 0 15px;
  z-index: 100;
}

/* 移动端推荐商品区域 */
.mobile-recommended-section {
  display: none;
  width: 100%;
  margin: 30px auto;
  padding: 0 20px;
}

.mobile-recommended-title {
  font-size: 18px;
  color: #333;
  margin-bottom: 20px;
  font-weight: normal;
  text-align: center;
}

.mobile-recommended-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.mobile-recommended-item {
  text-align: center;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.mobile-recommended-image {
  width: 100%;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  background: white;
  border-radius: 4px;
}

.mobile-recommended-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.mobile-recommended-info {
  text-align: center;
}

.mobile-recommended-item-title {
  font-size: 13px;
  color: #333;
  margin-bottom: 5px;
  line-height: 1.3;
}

.mobile-recommended-badge {
  font-size: 10px;
  color: #e53935;
  background: #fff;
  padding: 2px 6px;
  border-radius: 8px;
  display: inline-block;
  border: 1px solid #e53935;
}

.recommended-title {
  font-size: 16px;
  color: #333;
  text-align: left;
  margin: 0 0 20px 0;
  font-weight: normal;
  background-color: #f6f7f8;
  padding: 5px;
}

.recommended-arrow {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 30px;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.recommended-arrow:hover {
  opacity: 0.7;
}

.recommended-arrow.up {
  margin-bottom: 15px;
}

.recommended-arrow.down {
  margin-top: 15px;
}

.recommended-list {
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.recommended-list::-webkit-scrollbar {
  display: none;
}

.recommended-item {
  margin-bottom: 20px;
  text-align: center;
}

.recommended-item:last-child {
  margin-bottom: 0;
}

.recommended-image {
  width: 100%;
  height: 120px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 4px;
}

.recommended-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.recommended-info {
  text-align: center;
}

.recommended-item-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  line-height: 1.3;
}

.recommended-badge {
  font-size: 12px;
  color: #e53935;
  background: #fff;
  padding: 2px 8px;
  border-radius: 10px;
  display: inline-block;
  border: 1px solid #e53935;
}

/* 产品参数区域 */
.product-params-section {
  margin: 0 auto 20px;
  padding: 0 30px;
  border-radius: 8px;
}

.params-section-title {
  font-size: 16px;
  color: #333;
  margin: 0 0 30px 0;
  font-weight: normal;
  background: #f6f7f8;
  padding: 5px;
}

.params-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.params-row {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.param-item {
  flex: 1;
  display: flex;
  align-items: flex-start;
  min-width: 0;
}

.param-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  margin-right: 8px;
  min-width: fit-content;
}

.param-value {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  word-wrap: break-word;
  flex: 1;
}

/* 产品详情选项卡 */
.product-tabs {
  margin: 40px auto;
}

.tabs-header {
  display: flex;
  border-bottom: 1px solid #eee;
}

.tab-item {
  padding: 15px 30px;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #1e8e3e;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #1e8e3e;
}

.tabs-content {
  padding: 30px 0;
}

.product-description {
  margin-bottom: 30px;
}

.product-description h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 15px;
}

.product-description p {
  line-height: 1.8;
  color: #666;
}

.product-images {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-image {
  width: 100%;
  height: auto;
}

.params-table {
  width: 100%;
  border-collapse: collapse;
}

.params-table tr {
  border-bottom: 1px solid #eee;
}

.params-table td {
  padding: 12px;
}

.param-name {
  width: 120px;
  color: #666;
  background-color: #f9f9f9;
}

.param-value {
  color: #333;
}

/* 相关推荐 */
.related-products {
  margin: 40px auto;
}

.section-title {
  font-size: 20px;
  color: #333;
  margin-bottom: 20px;
  position: relative;
  padding-left: 15px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: #1e8e3e;
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.related-item {
  border: 1px solid #eee;
  padding: 10px;
  transition: all 0.3s;
}

.related-item:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.related-image {
  width: 100%;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.related-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.related-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.related-price {
  color: #e53935;
  font-weight: bold;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .product-detail-page {
    padding: 0 20px;
  }

  .product-showcase {
    gap: 30px;
    margin: 20px auto;
  }

  .main-image {
    max-width: 450px;
  }

  .recommended-products {
    width: 180px;
  }

  .related-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .product-detail-page {
    padding: 0 15px;
  }

  .product-showcase {
    flex-direction: column;
    gap: 30px;
    margin: 20px auto;
    padding: 0;
  }

  .product-gallery {
    width: 100%;
  }

  .main-image {
    max-width: 100%;
    height: auto;
  }

  .thumbnail-slider {
    justify-content: center;
  }

  .thumbnails {
    max-width: 300px;
    justify-content: center;
  }

  .product-info {
    width: 100%;
  }

  .product-title {
    font-size: 18px;
    margin-bottom: 25px;
  }

  .price-value {
    font-size: 28px;
  }

  .spec-options {
    gap: 8px;
  }

  .spec-option {
    padding: 6px 12px;
    font-size: 13px;
  }

  .delivery-options {
    gap: 8px;
  }

  .delivery-option {
    padding: 6px 12px;
    font-size: 13px;
  }

  .action-buttons {
    gap: 10px;
  }

  .buy-now-btn,
  .add-cart-btn {
    flex: 1;
    padding: 12px 20px;
    font-size: 15px;
  }

  /* 推荐商品移动端隐藏桌面端，显示移动端 */
  .desktop-recommended {
    display: none;
  }

  .mobile-recommended-section {
    display: block;
  }

  .mobile-recommended-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .mobile-recommended-item {
    padding: 12px;
  }

  .mobile-recommended-image {
    height: 80px;
  }

  /* 产品参数移动端优化 */
  .product-params-section {
    padding: 0 15px;
    margin-bottom: 30px;
  }

  .params-row {
    flex-direction: column;
    gap: 15px;
  }

  .param-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
  }

  .param-label {
    margin-right: 0;
    font-weight: 600;
    color: #333;
  }

  .related-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .related-item {
    padding: 15px;
  }

  .related-image {
    height: 120px;
  }
}

@media (max-width: 480px) {
  .product-detail-page {
    padding: 0 10px;
  }

  .product-showcase {
    margin: 15px auto;
    gap: 25px;
  }

  .product-title {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .price-row,
  .activity-row,
  .spec-row,
  .delivery-row,
  .shipping-row,
  .quantity-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 15px;
  }

  .price-label,
  .activity-label,
  .spec-label,
  .delivery-label,
  .shipping-label,
  .quantity-label {
    width: auto;
    font-weight: 600;
    color: #333;
  }

  .price-value {
    font-size: 24px;
  }

  .spec-options,
  .delivery-options {
    width: 100%;
    flex-wrap: wrap;
  }

  .spec-option,
  .delivery-option {
    flex: 1;
    min-width: calc(50% - 4px);
    text-align: center;
  }

  .shipping-info {
    font-size: 13px;
    line-height: 1.4;
  }

  .quantity-controls {
    align-self: flex-start;
  }

  .action-buttons {
    flex-direction: column;
    gap: 15px;
    margin-top: 25px;
  }

  .buy-now-btn,
  .add-cart-btn {
    width: 100%;
    padding: 15px;
    font-size: 16px;
  }

  /* 缩略图移动端优化 */
  .thumbnail-slider {
    flex-direction: column;
    gap: 15px;
  }

  .slider-arrow {
    display: none;
  }

  .thumbnails {
    max-width: 100%;
    justify-content: flex-start;
    padding: 0;
  }

  .thumbnail {
    width: 60px;
    height: 60px;
  }

  /* 产品参数超小屏优化 */
  .product-params-section {
    padding: 0 10px;
    margin-bottom: 25px;
  }

  .params-section-title {
    font-size: 15px;
    margin-bottom: 20px;
  }

  .param-item {
    padding: 12px;
  }

  .param-label {
    font-size: 13px;
  }

  .param-value {
    font-size: 13px;
  }

  .related-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .related-item {
    padding: 12px;
  }

  .related-image {
    height: 100px;
  }

  .related-title {
    font-size: 13px;
  }

  /* 移动端推荐商品超小屏优化 */
  .mobile-recommended-section {
    margin: 25px auto;
    padding: 0 10px;
  }

  .mobile-recommended-title {
    font-size: 16px;
    margin-bottom: 15px;
  }

  .mobile-recommended-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .mobile-recommended-item {
    padding: 10px;
    display: flex;
    align-items: center;
    text-align: left;
    gap: 12px;
  }

  .mobile-recommended-image {
    width: 60px;
    height: 60px;
    margin-bottom: 0;
    flex-shrink: 0;
  }

  .mobile-recommended-info {
    text-align: left;
    flex: 1;
  }

  .mobile-recommended-item-title {
    font-size: 12px;
    margin-bottom: 3px;
  }

  .mobile-recommended-badge {
    font-size: 9px;
    padding: 1px 4px;
  }
}
</style>