<template>
  <div class="order-payment-container">
    <!-- 头部 -->
    <div class="header">
      <div class="header-content">
        <button class="back-btn" @click="goBack">
          <i class="icon-back">←</i>
        </button>
        <h1 class="page-title">确认订单</h1>
      </div>
    </div>

    <div class="payment-content">
      <!-- 配送方式选择 -->
      <div class="delivery-section">
        <h2 class="section-title">配送方式</h2>
        <div class="delivery-options">
          <div 
            class="delivery-option" 
            :class="{ active: deliveryType === 'express' }"
            @click="selectDeliveryType('express')"
          >
            <div class="option-icon">
              <i class="icon-express">🚚</i>
            </div>
            <div class="option-content">
              <h3>快递配送</h3>
              <p>预计2-3天送达</p>
            </div>
            <div class="option-radio">
              <span class="radio" :class="{ checked: deliveryType === 'express' }"></span>
            </div>
          </div>
          
          <div 
            class="delivery-option" 
            :class="{ active: deliveryType === 'pickup' }"
            @click="selectDeliveryType('pickup')"
          >
            <div class="option-icon">
              <i class="icon-pickup">🏪</i>
            </div>
            <div class="option-content">
              <h3>上门自提</h3>
              <p>到店即可取货</p>
            </div>
            <div class="option-radio">
              <span class="radio" :class="{ checked: deliveryType === 'pickup' }"></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 收货地址 (快递配送时显示) -->
      <div class="address-section" v-if="deliveryType === 'express'">
        <div class="section-header">
          <h2 class="section-title">收货地址</h2>
          <button class="add-address-btn" @click="showAddressModal = true">
            + 新增地址
          </button>
        </div>
        
        <div class="address-list">
          <div 
            class="address-item" 
            v-for="(address, index) in addressList" 
            :key="index"
            :class="{ active: selectedAddressIndex === index }"
            @click="selectAddress(index)"
          >
            <div class="address-content">
              <div class="address-header">
                <span class="recipient-name">{{ address.name }}</span>
                <span class="recipient-phone">{{ address.phone }}</span>
                <span class="default-tag" v-if="address.isDefault">默认</span>
              </div>
              <div class="address-detail">
                {{ address.province }} {{ address.city }} {{ address.district }} {{ address.detail }}
              </div>
            </div>
            <div class="address-radio">
              <span class="radio" :class="{ checked: selectedAddressIndex === index }"></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 门店选择 (上门自提时显示) -->
      <div class="store-section" v-if="deliveryType === 'pickup'">
        <h2 class="section-title">选择门店</h2>
        <div class="store-list">
          <div 
            class="store-item" 
            v-for="(store, index) in storeList" 
            :key="index"
            :class="{ active: selectedStoreIndex === index }"
            @click="selectStore(index)"
          >
            <div class="store-content">
              <div class="store-header">
                <h3 class="store-name">{{ store.name }}</h3>
                <span class="store-distance">{{ store.distance }}</span>
              </div>
              <div class="store-address">{{ store.address }}</div>
              <div class="store-info">
                <span class="store-phone">📞 {{ store.phone }}</span>
                <span class="store-hours">🕐 {{ store.hours }}</span>
              </div>
            </div>
            <div class="store-radio">
              <span class="radio" :class="{ checked: selectedStoreIndex === index }"></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 商品信息 -->
      <div class="product-section">
        <h2 class="section-title">商品信息</h2>
        <div class="product-list">
          <div class="product-item" v-for="(product, index) in orderProducts" :key="index">
            <div class="product-image">
              <img :src="product.image" :alt="product.name" />
            </div>
            <div class="product-info">
              <h3 class="product-name">{{ product.name }}</h3>
              <p class="product-spec">{{ product.spec }}</p>
              <div class="product-price-qty">
                <span class="product-price">¥{{ product.price }}</span>
                <span class="product-qty">x{{ product.quantity }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 费用明细 -->
      <div class="cost-section">
        <h2 class="section-title">费用明细</h2>
        <div class="cost-list">
          <div class="cost-item">
            <span class="cost-label">商品总价</span>
            <span class="cost-value">¥{{ totalProductPrice }}</span>
          </div>
          <div class="cost-item" v-if="deliveryType === 'express'">
            <span class="cost-label">运费</span>
            <span class="cost-value">¥{{ shippingFee }}</span>
          </div>
          <div class="cost-item total">
            <span class="cost-label">实付款</span>
            <span class="cost-value">¥{{ totalAmount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部支付按钮 -->
    <div class="payment-footer">
      <div class="payment-info">
        <span class="total-label">合计：</span>
        <span class="total-amount">¥{{ totalAmount }}</span>
      </div>
      <button class="pay-btn" @click="handlePayment" :disabled="!canPay">
        立即支付
      </button>
    </div>

    <!-- 地址管理弹窗 -->
    <div class="modal-overlay" v-if="showAddressModal" @click="showAddressModal = false">
      <div class="address-modal" @click.stop>
        <div class="modal-header">
          <h3>新增收货地址</h3>
          <button class="close-btn" @click="showAddressModal = false">×</button>
        </div>
        <div class="modal-content">
          <div class="form-group">
            <label>收货人</label>
            <input v-model="newAddress.name" type="text" placeholder="请输入收货人姓名" />
          </div>
          <div class="form-group">
            <label>手机号</label>
            <input v-model="newAddress.phone" type="tel" placeholder="请输入手机号" />
          </div>
          <div class="form-group">
            <label>所在地区</label>
            <div class="region-selector">
              <select v-model="newAddress.province">
                <option value="">请选择省份</option>
                <option value="广西">广西</option>
                <option value="广东">广东</option>
              </select>
              <select v-model="newAddress.city">
                <option value="">请选择城市</option>
                <option value="梧州市">梧州市</option>
                <option value="南宁市">南宁市</option>
              </select>
              <select v-model="newAddress.district">
                <option value="">请选择区县</option>
                <option value="万秀区">万秀区</option>
                <option value="长洲区">长洲区</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label>详细地址</label>
            <textarea v-model="newAddress.detail" placeholder="请输入详细地址"></textarea>
          </div>
          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" v-model="newAddress.isDefault" />
              设为默认地址
            </label>
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-btn" @click="showAddressModal = false">取消</button>
          <button class="confirm-btn" @click="addAddress">确认</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 配送方式
const deliveryType = ref('express') // 'express' | 'pickup'

// 地址相关
const selectedAddressIndex = ref(0)
const showAddressModal = ref(false)
const addressList = ref([
  {
    name: '张三',
    phone: '138****8888',
    province: '广西',
    city: '梧州市',
    district: '万秀区',
    detail: '某某街道某某小区1号楼101室',
    isDefault: true
  },
  {
    name: '李四',
    phone: '139****9999',
    province: '广西',
    city: '梧州市',
    district: '长洲区',
    detail: '某某路某某大厦2楼201室',
    isDefault: false
  }
])

const newAddress = ref({
  name: '',
  phone: '',
  province: '',
  city: '',
  district: '',
  detail: '',
  isDefault: false
})

// 门店相关
const selectedStoreIndex = ref(0)
const storeList = ref([
  {
    name: '梧州六堡茶旗舰店',
    address: '广西梧州市万秀区某某路123号',
    phone: '0774-1234567',
    hours: '09:00-21:00',
    distance: '1.2km'
  },
  {
    name: '梧州六堡茶体验店',
    address: '广西梧州市长洲区某某街456号',
    phone: '0774-7654321',
    hours: '09:00-20:00',
    distance: '2.5km'
  }
])

// 商品信息
const orderProducts = ref([
  {
    name: '茶陆壹号',
    spec: '500g/盒',
    price: 58,
    quantity: 2,
    image: '@/assets/images/products/sy_img1.png'
  },
  {
    name: '黑茶一茶饼竹礼盒',
    spec: '1000g/盒',
    price: 950,
    quantity: 1,
    image: '@/assets/images/products/ts_img1.png'
  }
])

// 费用计算
const totalProductPrice = computed(() => {
  return orderProducts.value.reduce((total, product) => {
    return total + (product.price * product.quantity)
  }, 0)
})

const shippingFee = computed(() => {
  return deliveryType.value === 'express' ? 10 : 0
})

const totalAmount = computed(() => {
  return totalProductPrice.value + shippingFee.value
})

// 是否可以支付
const canPay = computed(() => {
  if (deliveryType.value === 'express') {
    return addressList.value.length > 0 && selectedAddressIndex.value >= 0
  } else {
    return storeList.value.length > 0 && selectedStoreIndex.value >= 0
  }
})

// 方法
const goBack = () => {
  router.go(-1)
}

const selectDeliveryType = (type) => {
  deliveryType.value = type
}

const selectAddress = (index) => {
  selectedAddressIndex.value = index
}

const selectStore = (index) => {
  selectedStoreIndex.value = index
}

const addAddress = () => {
  if (!newAddress.value.name || !newAddress.value.phone || !newAddress.value.detail) {
    alert('请填写完整的地址信息')
    return
  }
  
  // 如果设为默认地址，取消其他地址的默认状态
  if (newAddress.value.isDefault) {
    addressList.value.forEach(addr => addr.isDefault = false)
  }
  
  addressList.value.push({ ...newAddress.value })
  
  // 重置表单
  newAddress.value = {
    name: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detail: '',
    isDefault: false
  }
  
  showAddressModal.value = false
}

const handlePayment = () => {
  if (!canPay.value) {
    alert('请选择配送方式和地址/门店')
    return
  }
  
  // 构建订单数据
  const orderData = {
    deliveryType: deliveryType.value,
    products: orderProducts.value,
    totalAmount: totalAmount.value,
    shippingFee: shippingFee.value
  }
  
  if (deliveryType.value === 'express') {
    orderData.address = addressList.value[selectedAddressIndex.value]
  } else {
    orderData.store = storeList.value[selectedStoreIndex.value]
  }
  
  console.log('订单数据:', orderData)
  
  // 跳转到支付页面
  router.push({
    path: '/payment',
    query: { orderId: Date.now() }
  })
}

onMounted(() => {
  // 页面初始化逻辑
})
</script>

<style scoped>
.order-payment-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px;
}

/* 头部样式 */
.header {
  background-color: #1c705e;
  color: white;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  height: 56px;
  max-width: 1200px;
  margin: 0 auto;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  margin-right: 16px;
}

.page-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

/* 内容区域 */
.payment-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 通用区块样式 */
.delivery-section,
.address-section,
.store-section,
.product-section,
.cost-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.add-address-btn {
  background: #1c705e;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

/* 配送方式样式 */
.delivery-options {
  display: flex;
  gap: 16px;
}

.delivery-option {
  flex: 1;
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
}

.delivery-option.active {
  border-color: #1c705e;
  background-color: #f0f8f5;
}

.option-icon {
  font-size: 24px;
}

.option-content h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #333;
}

.option-content p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.option-radio {
  margin-left: auto;
}

.radio {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 50%;
  position: relative;
}

.radio.checked {
  border-color: #1c705e;
}

.radio.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  background-color: #1c705e;
  border-radius: 50%;
}

/* 地址列表样式 */
.address-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.address-item {
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.address-item.active {
  border-color: #1c705e;
  background-color: #f0f8f5;
}

.address-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.recipient-name {
  font-weight: 600;
  color: #333;
}

.recipient-phone {
  color: #666;
}

.default-tag {
  background-color: #1c705e;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.address-detail {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

/* 门店列表样式 */
.store-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.store-item {
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.store-item.active {
  border-color: #1c705e;
  background-color: #f0f8f5;
}

.store-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.store-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.store-distance {
  color: #1c705e;
  font-size: 14px;
  font-weight: 500;
}

.store-address {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.store-info {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #666;
}

/* 商品列表样式 */
.product-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.product-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  flex: 1;
}

.product-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.product-spec {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
}

.product-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 16px;
  font-weight: 600;
  color: #e53935;
}

.product-qty {
  font-size: 14px;
  color: #666;
}

/* 费用明细样式 */
.cost-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.cost-item.total {
  border-top: 1px solid #e5e5e5;
  padding-top: 12px;
  font-weight: 600;
  font-size: 16px;
}

.cost-label {
  color: #666;
}

.cost-value {
  color: #333;
  font-weight: 500;
}

.cost-item.total .cost-value {
  color: #e53935;
  font-size: 18px;
}

/* 底部支付按钮 */
.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px 20px;
  border-top: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.payment-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.total-label {
  font-size: 16px;
  color: #666;
}

.total-amount {
  font-size: 20px;
  font-weight: 600;
  color: #e53935;
}

.pay-btn {
  background-color: #1c705e;
  color: white;
  border: none;
  padding: 12px 32px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.pay-btn:hover {
  background-color: #155a4a;
}

.pay-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.address-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e5e5;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-content {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  box-sizing: border-box;
}

.form-group textarea {
  height: 80px;
  resize: vertical;
}

.region-selector {
  display: flex;
  gap: 8px;
}

.region-selector select {
  flex: 1;
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e5e5e5;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background-color: #1c705e;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .payment-content {
    padding: 16px;
  }
  
  .delivery-options {
    flex-direction: column;
  }
  
  .delivery-option {
    flex-direction: row;
  }
  
  .region-selector {
    flex-direction: column;
  }
  
  .store-info {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .modal-footer {
    flex-direction: column;
  }
}
</style>
