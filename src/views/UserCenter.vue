<template>
  <div class="user-center-page">
    <Header/>

    <!-- 账号信息区域 -->
    <div class="account-info-section">
      <div class="section-header">
        <h2>账号信息</h2>
      </div>
      <div class="account-content">
        <div class="user-avatar">
          <div class="avatar-circle">
            <span class="avatar-text">顾</span>
          </div>
        </div>
        <div class="user-info">
          <h3 class="user-name">顾客名称</h3>
          <div class="user-actions">
            <div class="action-item">
              <span>编辑账号/密码</span>
            </div>
            <div class="action-item">
              <span>绑定手机</span>
            </div>
          </div>
          <div class="user-actions">
            <div class="action-item">
              <span>收货地址管理</span>
            </div>
            <div class="action-item">
              <span>账号注销</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 我的订单区域 -->
    <div class="order-section">
      <div class="section-header">
        <h2>我的订单</h2>
      </div>
      <div class="order-menu">
        <div class="order-menu-item">
          <div class="menu-icon">
            <img src="@/assets/images/用户中心_03.jpg" alt="全部订单" class="icon-image">
          </div>
          <span>全部订单</span>
        </div>
        <div class="order-menu-item">
          <div class="menu-icon">
            <img src="@/assets/images/用户中心_05.jpg" alt="待付款" class="icon-image">
          </div>
          <span>待付款</span>
        </div>
        <div class="order-menu-item">
          <div class="menu-icon">
            <img src="@/assets/images/用户中心_07.jpg" alt="待发货/备货" class="icon-image">
          </div>
          <span>待发货/备货</span>
        </div>
        <div class="order-menu-item">
          <div class="menu-icon">
            <img src="@/assets/images/用户中心_09.jpg" alt="待收货/提货" class="icon-image">
          </div>
          <span>待收货/提货</span>
        </div>
      </div>
    </div>

    <!-- 会员权益区域 -->
    <div class="benefits-section">
      <div class="section-header">
        <h2>会员权益</h2>
      </div>
      <div class="benefits-menu">
        <div class="benefit-menu-item">
          <div class="menu-icon">
            <img src="@/assets/images/用户中心_16.jpg" alt="优惠券" class="icon-image">
          </div>
          <span>优惠券</span>
        </div>
        <div class="benefit-menu-item">
          <div class="menu-icon">
            <img src="@/assets/images/用户中心_17.jpg" alt="退换货" class="icon-image">
          </div>
          <span>退换货</span>
        </div>
        <div class="benefit-menu-item">
          <div class="menu-icon">
            <img src="@/assets/images/用户中心_19.jpg" alt="在线客服" class="icon-image">
          </div>
          <span>在线客服</span>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <Footer/>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Header from "@/components/Header.vue";
import Footer from "@/components/Footer.vue";

// 用户数据
const userData = ref({
  name: '用户名称',
  level: '普通会员/等级',
  memberSince: '七年会员资格',
  avatar: 'https://picsum.photos/100/100?random=1'
})
</script>

<style scoped>
.user-center-page {
  width: 1200px;
  margin: 0 auto;
  background-color: white;
}

/* 账号信息区域 */
.account-info-section {
  margin: 30px auto;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  background-color: #1c705e;
  padding: 15px 20px;
}

.section-header h2 {
  color: #fff;
  margin: 0;
  font-size: 18px;
  font-weight: normal;
}

.account-content {
  display: flex;
  align-items: center;
  padding: 30px;
  background-color: #f6f7f8;
}

.user-avatar {
  margin-right: 30px;
}

.avatar-circle {
  width: 110px;
  height: 110px;
  border-radius: 50%;
  background-color: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #666;
  font-weight: bold;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 20px;
  color: #333;
  margin: 0 0 20px 0;
  font-weight: normal;
}

.user-actions {
  display: flex;
  gap: 40px;
  margin-bottom: 15px;
}

.user-actions:last-child {
  margin-bottom: 0;
}

.action-item {
  color: #52a58a;
  cursor: pointer;
  font-size: 14px;
  text-decoration: none;
}

.action-item:hover {
  text-decoration: underline;
}

/* 订单区域 */
.order-section, .benefits-section {
  margin: 30px auto;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.order-menu, .benefits-menu {
  display: flex;
  justify-content: space-around;
  padding: 40px 20px;
  background-color: #f6f7f8;
}

.order-menu-item, .benefit-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  flex: 1;
  max-width: 200px;
}

.menu-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.icon-image {
  width: 50px;
  height: 50px;
  object-fit: contain;
}

.order-menu-item span, .benefit-menu-item span {
  font-size: 14px;
  color: #333;
  text-align: center;
  line-height: 1.4;
}


/* 响应式调整 */
@media (max-width: 1024px) {
  .user-center-page {
    width: 95%;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .account-content {
    flex-direction: column;
    text-align: center;
  }

  .user-avatar {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .user-actions {
    justify-content: center;
    gap: 20px;
  }

  .order-menu, .benefits-menu {
    flex-wrap: wrap;
    gap: 20px;
    padding: 30px 15px;
  }

  .order-menu-item, .benefit-menu-item {
    width: 45%;
    max-width: none;
  }
}

@media (max-width: 480px) {
  .user-actions {
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }

  .order-menu, .benefits-menu {
    flex-direction: column;
    gap: 30px;
    align-items: center;
  }

  .order-menu-item, .benefit-menu-item {
    width: 80%;
    max-width: 200px;
  }
}
</style>