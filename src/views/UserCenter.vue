<template>
  <div class="user-center-page">
    <Header/>
    <!-- 用户信息区域 -->
    <div class="user-info-section container">
      <div class="user-avatar">
        <img src="https://picsum.photos/100/100?random=1" alt="用户头像" />
      </div>
      <div class="user-details">
        <div class="user-name">
          <h3>用户名称</h3>
          <p class="user-id">普通会员/等级</p>
          <p class="user-since">七年会员资格</p>
        </div>
        <div class="user-actions">
          <div class="action-item">
            <i class="el-icon-message"></i>
            <span>修改资料</span>
          </div>
          <div class="action-item">
            <i class="el-icon-lock"></i>
            <span>修改密码</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 我的订单区域 -->
    <div class="order-section container">
      <div class="section-header">
        <h2>我的订单</h2>
      </div>
      <div class="order-menu">
        <div class="order-menu-item">
          <div class="menu-icon">
            <i class="el-icon-document"></i>
          </div>
          <span>全部订单</span>
        </div>
        <div class="order-menu-item">
          <div class="menu-icon">
            <i class="el-icon-wallet"></i>
          </div>
          <span>待付款</span>
        </div>
        <div class="order-menu-item">
          <div class="menu-icon">
            <i class="el-icon-box"></i>
          </div>
          <span>待发货/待收</span>
        </div>
        <div class="order-menu-item">
          <div class="menu-icon">
            <i class="el-icon-star-off"></i>
          </div>
          <span>待点评/晒单</span>
        </div>
      </div>
    </div>

    <!-- 会员权益区域 -->
    <div class="benefits-section container">
      <div class="section-header">
        <h2>会员权益</h2>
      </div>
      <div class="benefits-menu">
        <div class="benefit-menu-item">
          <div class="menu-icon">
            <i class="el-icon-ticket"></i>
          </div>
          <span>优惠券</span>
        </div>
        <div class="benefit-menu-item">
          <div class="menu-icon">
            <i class="el-icon-coin"></i>
          </div>
          <span>积分商城</span>
        </div>
        <div class="benefit-menu-item">
          <div class="menu-icon">
            <i class="el-icon-headset"></i>
          </div>
          <span>会员客服</span>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <Footer/>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Header from "@/components/Header.vue";
import Footer from "@/components/Footer.vue";

// 用户数据
const userData = ref({
  name: '用户名称',
  level: '普通会员/等级',
  memberSince: '七年会员资格',
  avatar: 'https://picsum.photos/100/100?random=1'
})
</script>

<style scoped>
.user-center-page {
  width: 100%;
}

/* 用户信息区域 */
.user-info-section {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 20px;
  margin: 20px auto;
  border-radius: 8px;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
  border: 2px solid #1e8e3e;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-name h3 {
  font-size: 18px;
  color: #333;
  margin: 0 0 5px 0;
}

.user-id, .user-since {
  margin: 5px 0;
  color: #666;
  font-size: 14px;
}

.user-actions {
  display: flex;
  gap: 20px;
}

.action-item {
  display: flex;
  align-items: center;
  color: #1e8e3e;
  cursor: pointer;
}

.action-item i {
  margin-right: 5px;
}

/* 订单区域 */
.order-section, .benefits-section {
  margin: 30px auto;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
  background-color: #1e8e3e;
  padding: 15px 20px;
}

.section-header h2 {
  color: #fff;
  margin: 0;
  font-size: 18px;
  font-weight: normal;
}

.order-menu, .benefits-menu {
  display: flex;
  justify-content: space-around;
  padding: 30px 0;
}

.order-menu-item, .benefit-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.menu-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.menu-icon i {
  font-size: 24px;
  color: #1e8e3e;
}


/* 响应式调整 */
@media (max-width: 1024px) {
  .footer-sections {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .user-details {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .user-actions {
    margin-top: 15px;
  }
  
  .order-menu, .benefits-menu {
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .order-menu-item, .benefit-menu-item {
    width: 45%;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .user-info-section {
    flex-direction: column;
    text-align: center;
  }
  
  .user-avatar {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .user-details {
    width: 100%;
    align-items: center;
  }
  
  .order-menu-item, .benefit-menu-item {
    width: 100%;
  }
  
  .footer-sections {
    grid-template-columns: 1fr;
  }
}
</style>