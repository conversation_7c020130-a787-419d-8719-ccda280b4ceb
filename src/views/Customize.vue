<template>
  <div>
    <Header scroll placeholder></Header>
    <div class="flex justify-center item-center relative">
      <img src="@/assets/activity/ppdt1.jpg" alt="" class="banner-img">
      <div class="absolute w-full flex justify-center item-center flex-col text-white" style="height: 350px">
        <H2 style="margin: 0">茶叶定制</H2>
        <p style="margin: 10px 0; color: rgba(255,255,255,.7)">CUSTOMIZE</p>
      </div>
    </div>
    <div class="flex justify-center item-center" style="margin-top: 2rem">
      <div class="btn">茶叶定制</div>
    </div>
    <div class="bg">

      <section class="tea-custom-section">
        <div class="container">
          <div class="tea-custom-content">
            <!-- 左侧传统建筑图片 -->
            <div class="custom-image">
              <img src="@/assets/customize/tea-custom-building.png" alt="传统茶厂建筑" class="custom-building-img">
            </div>

            <!-- 右侧内容 -->
            <div class="custom-info">
              <div class="custom-header">
                <h2 class="custom-title">茶叶定制</h2>
                <div class="custom-underline"></div>
              </div>

              <div class="custom-description">
                <p>
                  承传自身强大的茶叶制造体系，"茶船古道"致力于自主产品的研发生产，覆盖绿茶4000多个SKU，覆盖绿、白、黄、红、乌龙等各类茶叶品种，在茶叶生产、加工、储存、销售、茶艺、河池、茶道、河南、江西等各个环节，安徽等地拥有茶叶综合体工厂十余个，实现了从茶园到茶杯的全产业链整合，我们有能力为您提供专业的茶叶定制服务，以满足客户，以优质茶叶，以优质文，"茶船古道"让茶走入人心上。</p>
              </div>

              <!-- 制茶工艺流程图片 -->
              <div class="custom-process">
                <div class="process-item">
                  <img src="@/assets/customize/tea-custom-0.png" alt="茶叶采摘" class="process-img">
                </div>
                <div class="process-item">
                  <img src="@/assets/customize/tea-custom-1.png" alt="茶叶加工" class="process-img">
                </div>
                <div class="process-item">
                  <img src="@/assets/customize/tea-custom-2.png" alt="茶叶筛选" class="process-img">
                </div>
                <div class="process-item">
                  <img src="@/assets/customize/tea-custom-3.png" alt="茶叶包装" class="process-img">
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

    </div>
    <Footer></Footer>
    <SettleIn/>
  </div>
</template>

<script setup>
import {ref} from 'vue'

import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import SettleIn from "@/components/SettleIn.vue";

</script>

<style scoped>
.btn {
  background-color: #1f7260;
  border: 1px solid white;
  padding: 10px 30px;
  color: white;
}

.banner-img {
  height: 350px;
  object-fit: cover;
  width: 100%
}

.bg {
  background: url("@/assets/contact/lxwm_bj.png");
  background-size: 100% 100%;
  padding: 2rem;
  background-position-y: center;
  background-repeat: no-repeat;
  backdrop-filter: opacity(0.1);
}

.shadow {
  box-shadow: 0 20px 30px rgba(30, 114, 96, 0.5);
}


/* 茶叶定制区域 */
.tea-custom-section {
  padding: 80px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.tea-custom-content {
  display: flex;
  align-items: flex-start;
  gap: 60px;
}

.custom-image {
  flex: 1;
  max-width: 500px;
}

.custom-building-img {
  width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.custom-info {
  flex: 1.2;
  padding-left: 40px;
}

.custom-header {
  margin-bottom: 30px;
}

.custom-title {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  margin: 0 0 15px 0;
}

.custom-underline {
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #1f7260 0%, #4CAF50 100%);
  border-radius: 2px;
}

.custom-description {
  margin-bottom: 40px;
}

.custom-description p {
  font-size: 16px;
  line-height: 1.8;
  color: #666;
  text-align: justify;
  margin: 0;
}

.custom-process {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.process-item {
  text-align: center;
}

.process-img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.process-img:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-img {
    height: 200px;
  }

  .tea-custom-content {
    flex-direction: column;
    gap: 40px;
  }

  .custom-info {
    padding-left: 0;
  }

  .custom-process {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .custom-title {
    font-size: 28px;
  }
}

/* 额外的响应式优化 */
@media (max-width: 1024px) {

  .bg {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .banner-img {
    height: 200px;
  }

  .bg {
    padding: 1rem;
    background-size: cover;
  }

}

@media (max-width: 480px) {
  .bg {
    padding: 0.5rem;
  }

  .banner-img {
    height: 200px;
  }

  .btn {
    padding: 8px 20px;
    font-size: 14px;
  }
}
</style>