<template>
  <div>
    <Header scroll placeholder></Header>
    <div class="banner-section">
      <div class="banner-container">
        <img src="@/assets/about/gywm_banner.png" alt="招商加盟" class="banner-img"
             :class="{ 'mobile-banner': isMobile }">
        <div class="banner-overlay">
          <div class="banner-content">
            <h2 class="banner-title">招商加盟</h2>
            <p class="banner-subtitle">INVESTMENT</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 招商加盟主要内容区域 -->
    <section class="investment-section">
      <!-- 背景图片 -->
      <div class="investment-bg">
        <img src="@/assets/index/zs_bg.jpg" alt="招商加盟背景" class="bg-img">
      </div>

      <!-- 桌面端布局 -->
      <div v-if="!isMobile" class="investment-content-desktop">
        <div class="form-wrap">
          <img src="@/assets/index/zsjm.png" class="w-full" alt="">
          <p class="text-left" style="margin: 2rem 0; color: rgba(255,255,255,0.8)">销售覆盖到北上广及周边区域</p>
          <p style="color: rgba(255,255,255,0.8); margin-bottom: 2rem">
            以南宁、澳门为纽带桥梁，拓展东南亚、欧美国家等市场。</p>

          <div class="flex justify-start item-center" style="margin-top: 1rem">
            <span style="width: 80px; color: rgba(255,255,255,0.8)">姓名: </span>
            <input name="name" class="investment-form-input" v-model="name" placeholder="必填"></input>
          </div>
          <div class="flex justify-start item-center" style="margin: 1rem 0">
            <span style="width: 80px; color: rgba(255,255,255,0.8)">手机: </span>
            <input name="phone" class="investment-form-input" v-model="name" placeholder="必填"></input>
          </div>
          <div class="flex justify-start item-center" style="margin-top: 1rem">
            <span style="width: 80px; color: rgba(255,255,255,0.8)">备注: </span>
            <textarea name="remark" class="investment-form-input" v-model="name"
                      placeholder="有无门店，意向城市等"></textarea>
          </div>
          <div style="margin: 30px 0 30px 70px">
            <el-button type="primary"
                       style="border-radius: 999px; background-color: white; color: #961D13; width: 140px">
              加入我们
            </el-button>
          </div>
          <p style="margin-top: 1rem; font-size: 2rem; font-weight: bold">加盟热线: 400-8888-888</p>
        </div>
      </div>

      <!-- 移动端布局 -->
      <div v-else class="investment-content-mobile">
        <div class="mobile-overlay">
          <div class="mobile-header">
            <img src="@/assets/index/zsjm.png" alt="招商加盟" class="mobile-logo">
            <h3 class="mobile-title">加盟合作</h3>
            <div class="mobile-description">
              <p>销售覆盖全国主要城市</p>
              <p>拓展东南亚、欧美等海外市场</p>
            </div>
          </div>

          <div class="mobile-form-container">
            <form class="mobile-form" @submit.prevent="submitForm">
              <div class="mobile-form-group">
                <input
                  v-model="formData.name"
                  type="text"
                  class="mobile-input"
                  placeholder="请输入您的姓名 *"
                  :class="{ 'error': formErrors.name }"
                >
                <span v-if="formErrors.name" class="mobile-error">{{ formErrors.name }}</span>
              </div>

              <div class="mobile-form-group">
                <input
                  v-model="formData.phone"
                  type="tel"
                  class="mobile-input"
                  placeholder="请输入您的手机号 *"
                  :class="{ 'error': formErrors.phone }"
                >
                <span v-if="formErrors.phone" class="mobile-error">{{ formErrors.phone }}</span>
              </div>

              <div class="mobile-form-group">
                <textarea
                  v-model="formData.remark"
                  class="mobile-textarea"
                  placeholder="备注信息（有无门店，意向城市等）"
                  rows="3"
                ></textarea>
              </div>

              <div class="mobile-actions">
                <button type="submit" class="mobile-submit-btn">立即加盟</button>
                <button type="button" class="mobile-call-btn" @click="callPhone">
                  <span class="phone-icon">📞</span>
                  400-8888-888
                </button>
              </div>
            </form>
          </div>

          <!-- 移动端特色功能 -->
          <div class="mobile-features">
            <div class="feature-item">
              <div class="feature-icon">🏪</div>
              <div class="feature-text">
                <h4>门店支持</h4>
                <p>全方位开店指导</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📈</div>
              <div class="feature-text">
                <h4>品牌优势</h4>
                <p>知名茶叶品牌</p>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🤝</div>
              <div class="feature-text">
                <h4>合作共赢</h4>
                <p>共享发展机遇</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <Footer></Footer>
    <SettleIn/>
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted} from 'vue'
import {ElMessage} from 'element-plus'

import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import SettleIn from "@/components/SettleIn.vue";

const isMobile = ref(false)
const deviceInfo = ref({
  type: '',
  os: '',
  browser: '',
  screenWidth: 0
})

// 表单数据
const formData = ref({
  name: '',
  phone: '',
  email: '',
  city: '',
  experience: '',
  investment: '',
  storeType: '',
  remark: ''
})

// 表单验证状态
const formErrors = ref({
  name: '',
  phone: '',
  email: '',
  city: '',
  experience: '',
  investment: '',
  storeType: '',
  remark: ''
})

// 投资金额选项
const investmentOptions = [
  '10-30万', '30-50万', '50-100万', '100-200万', '200万以上'
]

// 经验选项
const experienceOptions = [
  '无经验', '1-3年', '3-5年', '5年以上'
]

// 门店类型选项
const storeTypeOptions = [
  '街边店', '商场店', '社区店', '写字楼店', '其他'
]

// 获取设备信息
const getDeviceInfo = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  const screenWidth = window.innerWidth

  // 检测操作系统
  let os = 'Unknown'
  if (userAgent.includes('android')) os = 'Android'
  else if (userAgent.includes('iphone') || userAgent.includes('ipad')) os = 'iOS'
  else if (userAgent.includes('windows')) os = 'Windows'
  else if (userAgent.includes('mac')) os = 'macOS'
  else if (userAgent.includes('linux')) os = 'Linux'

  // 检测浏览器
  let browser = 'Unknown'
  if (userAgent.includes('chrome')) browser = 'Chrome'
  else if (userAgent.includes('firefox')) browser = 'Firefox'
  else if (userAgent.includes('safari')) browser = 'Safari'
  else if (userAgent.includes('edge')) browser = 'Edge'

  // 检测设备类型
  let type = 'Desktop'
  if (userAgent.includes('mobile') || screenWidth <= 768) type = 'Mobile'
  else if (userAgent.includes('tablet') || (screenWidth > 768 && screenWidth <= 1024)) type = 'Tablet'

  deviceInfo.value = {type, os, browser, screenWidth}

  return {type, os, browser, screenWidth}
}

// 检测是否为移动设备
const checkMobile = () => {
  const {type, screenWidth} = getDeviceInfo()
  const userAgent = navigator.userAgent.toLowerCase()

  // 更精确的移动设备检测
  const mobileKeywords = ['mobile', 'android', 'iphone', 'ipod', 'blackberry', 'windows phone']
  const tabletKeywords = ['ipad', 'tablet']

  const isMobileDevice = mobileKeywords.some(keyword => userAgent.includes(keyword))
  const isTabletDevice = tabletKeywords.some(keyword => userAgent.includes(keyword))
  const isSmallScreen = screenWidth <= 768
  const isMediumScreen = screenWidth > 768 && screenWidth <= 1024

  // 移动设备或小屏幕显示移动端界面
  isMobile.value = isMobileDevice || isSmallScreen || (isTabletDevice && isMediumScreen)
}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
}

// 表单验证
const validateForm = () => {
  let isValid = true
  formErrors.value = {
    name: '', phone: '', email: '', city: '',
    experience: '', investment: '', storeType: '', remark: ''
  }

  // 验证姓名
  if (!formData.value.name.trim()) {
    formErrors.value.name = '请输入姓名'
    isValid = false
  }

  // 验证手机号
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!formData.value.phone.trim()) {
    formErrors.value.phone = '请输入手机号'
    isValid = false
  } else if (!phoneRegex.test(formData.value.phone)) {
    formErrors.value.phone = '请输入正确的手机号格式'
    isValid = false
  }

  // 验证邮箱（可选）
  if (formData.value.email.trim()) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.value.email)) {
      formErrors.value.email = '请输入正确的邮箱格式'
      isValid = false
    }
  }

  // 验证城市
  if (!formData.value.city.trim()) {
    formErrors.value.city = '请输入意向城市'
    isValid = false
  }

  return isValid
}

// 提交表单
const submitForm = () => {
  if (validateForm()) {
    // 这里可以添加实际的提交逻辑
    ElMessage.success('提交成功！我们会尽快联系您')
    console.log('表单数据:', formData.value)

    // 重置表单
    formData.value = {
      name: '', phone: '', email: '', city: '',
      experience: '', investment: '', storeType: '', remark: ''
    }
  } else {
    ElMessage.error('请检查表单信息')
  }
}

// 拨打电话
const callPhone = () => {
  window.open('tel:400-8888-888')
}

// 组件挂载时初始化
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 基础样式 */
.btn {
  background-color: #1f7260;
  border: 1px solid white;
  padding: 10px 30px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.btn:hover {
  background-color: #155a4a;
  transform: translateY(-2px);
}

/* 横幅区域样式 */
.banner-section {
  position: relative;
}

.banner-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.banner-img {
  width: 100%;
  height: 350px;
  object-fit: cover;
}

.mobile-banner {
  height: 250px;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
}

.banner-content {
  text-align: center;
  color: white;
}

.banner-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-subtitle {
  font-size: 1.2rem;
  margin: 20px 0;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* 招商加盟主要区域样式 */
.investment-section {
  position: relative;
  min-height: 800px;
  overflow: hidden;
}

.investment-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.bg-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 桌面端表单样式 */
.investment-content-desktop {
  position: relative;
  z-index: 10;
  height: 798px;

  .form-wrap {
    position: absolute;
    left: 160px;
    top: 15%;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-direction: column;
    color: white;
  }
}

.investment-form-input {
  flex: 1;
  padding: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background-color: rgba(255, 255, 255, .5);
  border: none;
}

.investment-form-input::placeholder {
  color: rgba(255, 255, 255, .5);
}


.investment-form-desktop {
  position: absolute;
  left: 160px;
  top: 100px;
  max-width: 450px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-header {
  margin-bottom: 30px;
}

.form-logo {
  width: 100%;
  max-width: 300px;
  height: auto;
  margin-bottom: 20px;
}

.form-description {
  margin-bottom: 20px;
}

.desc-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  line-height: 1.6;
  margin: 10px 0;
}

.investment-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
}

.form-input,
.form-textarea {
  padding: 12px 16px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid transparent;
  border-radius: 8px;
  transition: all 0.3s ease;
  color: #333;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  background: rgba(255, 255, 255, 1);
  border-color: #1f7260;
  box-shadow: 0 0 0 3px rgba(31, 114, 96, 0.2);
}

.form-input.error,
.form-textarea.error {
  border-color: #e74c3c;
  background: rgba(255, 235, 235, 0.9);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #999;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.error-text {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
}

.form-actions {
  margin: 20px 0;
}

.submit-btn {
  background: linear-gradient(135deg, #1f7260 0%, #27ae60 100%);
  color: white;
  border: none;
  padding: 14px 40px;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(31, 114, 96, 0.3);
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(31, 114, 96, 0.4);
}

.submit-btn:active {
  transform: translateY(0);
}

.contact-info {
  text-align: center;
  margin-top: 20px;
}

.hotline {
  color: white;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: underline;
}

.hotline:hover {
  color: #27ae60;
  transform: scale(1.05);
}

/* 移动端样式 */
.investment-content-mobile {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 20px 0;
}

.mobile-overlay {
  width: 100%;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  margin: 20px;
  padding: 30px 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-header {
  text-align: center;
  margin-bottom: 30px;
}

.mobile-logo {
  width: 100%;
  max-width: 250px;
  height: auto;
  margin-bottom: 15px;
}

.mobile-title {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 15px 0;
}

.mobile-description {
  margin-top: 15px;
}

.mobile-description p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  line-height: 1.5;
  margin: 8px 0;
}

.mobile-form-container {
  margin-bottom: 30px;
}

.mobile-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.mobile-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.mobile-input,
.mobile-textarea {
  padding: 14px 16px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid transparent;
  border-radius: 12px;
  transition: all 0.3s ease;
  color: #333;
}

.mobile-input:focus,
.mobile-textarea:focus {
  outline: none;
  background: rgba(255, 255, 255, 1);
  border-color: #1f7260;
  box-shadow: 0 0 0 3px rgba(31, 114, 96, 0.2);
}

.mobile-input.error,
.mobile-textarea.error {
  border-color: #e74c3c;
  background: rgba(255, 235, 235, 0.95);
}

.mobile-input::placeholder,
.mobile-textarea::placeholder {
  color: #999;
  font-size: 14px;
}

.mobile-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.mobile-error {
  color: #ff6b6b;
  font-size: 12px;
  margin-top: 4px;
  padding-left: 4px;
}

.mobile-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 10px;
}

.mobile-submit-btn {
  background: linear-gradient(135deg, #1f7260 0%, #27ae60 100%);
  color: white;
  border: none;
  padding: 16px 20px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(31, 114, 96, 0.3);
}

.mobile-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(31, 114, 96, 0.4);
}

.mobile-call-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  padding: 14px 20px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.mobile-call-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.phone-icon {
  font-size: 18px;
}

/* 移动端特色功能 */
.mobile-features {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
  margin-top: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
}

.feature-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(31, 114, 96, 0.3);
  border-radius: 50%;
  flex-shrink: 0;
}

.feature-text {
  flex: 1;
}

.feature-text h4 {
  color: white;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.feature-text p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin: 0;
  line-height: 1.3;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-title {
    font-size: 2rem;
  }

  .banner-subtitle {
    font-size: 1rem;
    margin: 15px 0;
  }

  .investment-section {
    min-height: auto;
  }

  .mobile-overlay {
    margin: 15px;
    padding: 25px 15px;
    border-radius: 16px;
  }

  .mobile-title {
    font-size: 1.3rem;
  }

  .mobile-description p {
    font-size: 13px;
  }

  .mobile-input,
  .mobile-textarea {
    padding: 12px 14px;
    font-size: 15px;
  }

  .mobile-submit-btn,
  .mobile-call-btn {
    padding: 14px 18px;
    font-size: 15px;
  }

  .feature-item {
    padding: 12px;
  }

  .feature-icon {
    width: 35px;
    height: 35px;
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .banner-img {
    height: 200px;
  }

  .banner-title {
    font-size: 1.8rem;
  }

  .banner-subtitle {
    font-size: 0.9rem;
    margin: 10px 0;
  }

  .mobile-overlay {
    margin: 10px;
    padding: 20px 12px;
    border-radius: 12px;
  }

  .mobile-logo {
    max-width: 200px;
  }

  .mobile-title {
    font-size: 1.2rem;
  }

  .mobile-description p {
    font-size: 12px;
  }

  .mobile-form {
    gap: 15px;
  }

  .mobile-input,
  .mobile-textarea {
    padding: 10px 12px;
    font-size: 14px;
    border-radius: 8px;
  }

  .mobile-submit-btn,
  .mobile-call-btn {
    padding: 12px 16px;
    font-size: 14px;
    border-radius: 8px;
  }

  .mobile-features {
    gap: 12px;
  }

  .feature-item {
    padding: 10px;
    gap: 12px;
  }

  .feature-icon {
    width: 30px;
    height: 30px;
    font-size: 18px;
  }

  .feature-text h4 {
    font-size: 13px;
  }

  .feature-text p {
    font-size: 11px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .mobile-overlay {
    margin: 8px;
    padding: 15px 10px;
  }

  .mobile-logo {
    max-width: 180px;
  }

  .mobile-title {
    font-size: 1.1rem;
  }

  .mobile-input,
  .mobile-textarea {
    padding: 8px 10px;
    font-size: 13px;
  }

  .mobile-submit-btn,
  .mobile-call-btn {
    padding: 10px 14px;
    font-size: 13px;
  }

  .feature-item {
    padding: 8px;
    gap: 10px;
  }
}

/* 桌面端响应式优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  .investment-form-desktop {
    left: 80px;
    max-width: 400px;
    padding: 30px;
  }

  .form-logo {
    max-width: 250px;
  }

  .desc-text {
    font-size: 14px;
  }

  .form-input,
  .form-textarea {
    padding: 10px 14px;
    font-size: 14px;
  }

  .submit-btn {
    padding: 12px 30px;
    font-size: 14px;
  }

  .hotline {
    font-size: 18px;
  }
}
</style>