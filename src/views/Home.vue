<template>
  <div class="home-container">
    <Header :scroll="scroll"></Header>

    <!-- 产品展示区域 -->
    <div class="product-showcase">
      <div class="container">
        <div class="showcase-content">
          <!-- 左侧产品分类菜单 -->
          <div class="category-menu">
            <div class="category-item" v-for="(category, index) in productCategories" :key="index">
              <span class="category-name">{{ category.name }}</span>
              <span class="category-arrow">></span>
            </div>
          </div>

          <!-- 右侧产品展示 -->
          <el-carousel height="533px" style="width: 1160px;">
            <el-carousel-item style="height: 533px; width: 1160px">
              <img src="@/assets/images/商城PC端4(有字)_03.jpg" alt="" style="height: 533px; width: 1160px">
            </el-carousel-item>
            <el-carousel-item style="height: 533px; width: 1160px">
              <img src="@/assets/images/商城PC端4(有字)_031.png" alt="" style="height: 533px; width: 1160px">
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </div>

    <div class="flex justify-around items-center mx-auto mt-1" style="max-width: 1200px; margin-top: 1rem">
      <img :src="item" alt="" v-for="(item, index) in introImages" :key="index" style="flex: 0 0 260px;width: 260px"
           class="rounded">
    </div>

    <div class="flex justify-around items-center mx-auto mt-1" style="max-width: 1200px; margin-top: 1rem">
      <img :src="item" alt="" v-for="(item, index) in couponImages" :key="index"
           style="flex: 0 0 260px;width: 260px; object-fit: cover"
           class="rounded">
    </div>

    <!-- 茶叶产品展示区域 -->
    <div class="product-section container">
      <div class="section-header">
        <h2>活动热卖</h2>
        <el-button type="text" class="more-btn">查看更多 >></el-button>
      </div>
    </div>
    <div class="tea-products-section">
      <div class="container">
        <div class="tea-products-grid">
          <div class="tea-product-card" v-for="(product, index) in teaProducts" :key="index"
               :style="{ backgroundImage: `url(${product.backgroundImage})` }">
            <div class="tea-product-content">
              <h3 class="tea-product-title">{{ product.title }}</h3>
              <p class="tea-product-desc">{{ product.description }}</p>
              <div class="tea-product-footer">
                <span class="tea-product-price">{{ product.price }}</span>
                <button class="tea-product-btn">立即购买</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新品上市 -->
    <div class="product-section container">
      <div class="section-header">
        <h2>新品上市</h2>
        <el-button type="text" class="more-btn">查看更多 >></el-button>
      </div>
    </div>
    <div class="tea-products-section">
      <div class="container">
        <div class="tea-products-grid">
          <div class="tea-product-card" v-for="(product, index) in newProducts" :key="index"
               :style="{ backgroundImage: `url(${product.backgroundImage})` }">
            <div class="tea-product-content">
              <h3 class="tea-product-title">{{ product.title }}</h3>
              <p class="tea-product-desc">{{ product.description }}</p>
              <div class="tea-product-footer">
                <span class="tea-product-price">{{ product.price }}</span>
                <button class="tea-product-btn">立即购买</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 茶叶产品横幅展示模块 -->
    <block v-for="(item, index) in showcaseProducts" :key="index">
      <div class="product-section container">
        <div class="section-header">
          <h2>{{ item.title }}</h2>
          <el-button type="text" class="more-btn">查看更多 >></el-button>
        </div>
      </div>
      <div class="tea-banner-section">
        <div class="tea-banner-container" :style="{backgroundImage: `url(${item.bg})`}">
          <div class="tea-banner-content">
            <div class="tea-banner-text">
              <h2 class="tea-banner-title">{{ item.productTitle }}</h2>
              <p class="tea-banner-subtitle">{{ item.desc }}</p>
              <div class="tea-banner-price">¥{{ item.amount }}元/盒</div>
              <button class="tea-banner-btn">立即购买</button>
            </div>
          </div>
        </div>
      </div>
      <div class="products-showcase-section">
        <div class="products-showcase-container">
          <div class="product-showcase-item" v-for="(product, productIndex) in item.list" :key="productIndex">
            <div class="product-showcase-image">
              <img :src="product.image" :alt="product.title"/>
            </div>
            <div class="product-showcase-content">
              <h3 class="product-showcase-title">{{ product.title }}</h3>
              <p class="product-showcase-desc">{{ product.description }}</p>
              <div class="product-showcase-footer">
                <span class="product-showcase-price">{{ product.price }}</span>
                <button class="product-showcase-cart-btn">
                  <img src="@/assets/images/cart-green.png" alt="购物车"/>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </block>

    <div class="product-section container">
      <div class="section-header">
        <h2>片刻闲系列</h2>
        <el-button type="text" class="more-btn">查看更多 >></el-button>
      </div>
    </div>
    <!-- 片刻闲系列 -->
    <div class="tea-banner-section-pk">
      <div class="tea-banner-container" :style="{ backgroundImage: `url(${specialProductBg})` }">
        <div class="tea-banner-content">
          <div class="tea-banner-text">
            <h2 class="tea-banner-title">三个铁罐红色礼盒<br>(骑楼城)</h2>
            <p class="tea-banner-subtitle">茶条紧细匀整，黑褐，有少量茶梗，金花显。2017年中国—东盟博览会指定贵宾用茶</p>
            <div class="tea-banner-price">¥ 420元/盒</div>
            <button class="tea-banner-btn">立即购买</button>
          </div>
        </div>
      </div>
      <div class="product-list">
        <div v-for="(item, index) in pkxProductList" :key="index" class="item">
          <img :src="item.image" alt="">
          <div class="content-wrap">
            <H3>{{ item.title }}</H3>
            <p class="desc">{{ item.desc }}</p>
            <div class="flex justify-between items-center w-full">
              <span class="price">¥{{ item.amount }}元/盒</span>
              <span class="buy-text">立即购买>></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 特色茶叶展示区域 -->
    <div class="special-tea-section">
      <div class="container">
        <div class="special-tea-content">
          <div class="tea-product-card" v-for="(product, index) in specialTeaProducts" :key="index">
            <div class="product-image-container" :style="{ backgroundImage: `url(${specialSectionBg})` }">
              <div class="product-info">
                <h3 class="product-title">{{ product.title }}</h3>
                <p class="product-description">{{ product.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>关于我们</h4>
            <ul>
              <li>公司简介</li>
              <li>联系方式</li>
              <li>招贤纳士</li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>客户服务</h4>
            <ul>
              <li>配送方式</li>
              <li>支付方式</li>
              <li>售后服务</li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>商家合作</h4>
            <ul>
              <li>入驻条件</li>
              <li>合作方式</li>
              <li>商家中心</li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>关注我们</h4>
            <div class="social-icons">
              <i class="el-icon-s-custom"></i>
              <i class="el-icon-s-promotion"></i>
              <i class="el-icon-s-platform"></i>
            </div>
          </div>
        </div>
        <div class="copyright">
          © 2023 茶叶商城 版权所有
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted, computed} from 'vue'
import Header from '@/components/Header.vue'

const scroll = ref(false)
window.addEventListener('scroll', () => {
  // 顶部
  scroll.value = window.scrollY > 0
})

import introImage0 from '@/assets/images/intro/0.png'
import introImage1 from '@/assets/images/intro/1.png'
import introImage2 from '@/assets/images/intro/2.png'
import introImage3 from '@/assets/images/intro/3.png'

const introImages = [introImage0, introImage1, introImage2, introImage3]

import couponImage0 from '@/assets/images/coupon/30y.png'
import couponImage1 from '@/assets/images/coupon/100y.png'
import couponImage2 from '@/assets/images/coupon/1000y.png'

const couponImages = [couponImage0, couponImage1, couponImage2, couponImage2]

// 导入茶叶产品背景图片
import teaProduct1 from '@/assets/images/商城PC端4（无字）_35.jpg'
import teaProduct2 from '@/assets/images/商城PC端4（无字）_37.jpg'
import teaProduct3 from '@/assets/images/商城PC端4（无字）_39.jpg'

// 茶叶产品数据
const teaProducts = ref([
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
    price: '¥58元/盒',
    backgroundImage: teaProduct1
  },
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
    price: '¥58元/盒',
    backgroundImage: teaProduct2
  },
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
    price: '¥58元/盒',
    backgroundImage: teaProduct3
  }
])

import newProduct1 from '@/assets/images/商城PC端4（无字）_47.jpg'
import newProduct2 from '@/assets/images/商城PC端4（无字）_49.jpg'
import newProduct3 from '@/assets/images/商城PC端4（无字）_51.jpg'
// 茶叶产品数据
const newProducts = ref([
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
    price: '¥58元/盒',
    backgroundImage: newProduct1
  },
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
    price: '¥58元/盒',
    backgroundImage: newProduct2
  },
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
    price: '¥58元/盒',
    backgroundImage: newProduct3
  }
])

// 产品分类数据
const productCategories = ref([
  {name: '片刻闲系列'},
  {name: '及时雨系列'},
  {name: '盼秋叶系列'},
  {name: '听风闲香系列'},
  {name: '具象美物系列'},
  {name: '三原系列'},
  {name: '特色系列'},
  {name: '人文旅系列'},
  {name: '传统节庆系列'},
  {name: '经典系列'}
])

// 优惠券数据
const coupons = ref([
  {value: '¥30', desc: '满300元可用'},
  {value: '¥100', desc: '满1000元可用'},
  {value: '¥200', desc: '满2000元可用'},
  {value: '¥1000', desc: '满10000元可用'}
])

// 精选礼盒数据
const giftBoxes = ref([
  {title: '高档礼盒装', price: '688', image: 'https://picsum.photos/300/300?random=1'},
  {title: '精品茶叶礼盒', price: '888', image: 'https://picsum.photos/300/300?random=2'},
  {title: '尊享茶礼', price: '1288', image: 'https://picsum.photos/300/300?random=3'}
])

import productImage0 from '@/assets/images/products/sy_img1.png'
import productImage1 from '@/assets/images/products/sy_img2.png'
import productImage2 from '@/assets/images/products/sy_img3.png'
import productImage3 from '@/assets/images/products/sy_img4.png'
import productImage4 from '@/assets/images/products/sy_img5.png'

import productImage01 from '@/assets/images/products/ts_img1.png'
import productImage11 from '@/assets/images/products/ts_img2.png'
import productImage21 from '@/assets/images/products/ts_img3.png'
import productImage31 from '@/assets/images/products/ts_img4.png'
import productImage41 from '@/assets/images/products/ts_img5.png'

import productImage02 from '@/assets/images/products/rw_img1.png'
import productImage12 from '@/assets/images/products/rw_img2.png'
import productImage22 from '@/assets/images/products/rw_img3.png'
import productImage32 from '@/assets/images/products/rw_img4.png'
import productImage42 from '@/assets/images/products/rw_img5.png'

import mainProductImage0 from '@/assets/images/商城PC端4（无字）_57.jpg'
import mainProductImage1 from '@/assets/images/商城PC端4（无字）_74.jpg'
import mainProductImage2 from '@/assets/images/商城PC端4（无字）_90.jpg'

// 导入特殊产品背景图片
import specialProductBg from '@/assets/images/商城PC端4（无字）_108.jpg'
import specialSectionBg from '@/assets/images/商城PC端4（无字）_126.jpg'

// 产品展示数据
const showcaseProducts = ref([
  {
    title: '三原系列',
    productTitle: '茶陆壹号',
    desc: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
    amount: 58,
    bg: mainProductImage0,
    list: [
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage0
      },
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage1
      },
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage2
      },
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage3
      },
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage4
      }
    ]
  },
  {
    title: '特色系列',
    productTitle: '黑茶一茶饼竹礼盒',
    desc: '每一套礼盒都附带广西黑茶(六堡茶)产品质量监督检验中心的检验报告以及广西梧州六堡公司的独立收藏证书,岁月留金,极具收藏价值。',
    amount: 950,
    bg: mainProductImage1,
    list: [
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage01
      },
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage11
      },
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage21
      },
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage31
      },
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage41
      }
    ]
  },
  {
    title: '人文旅系列',
    productTitle: '三个铁罐红色礼盒(骑楼城)',
    desc: '茶条紧细匀整，⿊褐，有少量茶梗，⾦花显。2017年中国—东盟博览会指定贵宾⽤茶',
    amount: 420,
    bg: mainProductImage2,
    list: [
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage02
      },
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage12
      },
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage22
      },
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage32
      },
      {
        title: '茶陆壹号',
        description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
        price: '¥58元/盒',
        image: productImage42
      }
    ]
  }
])


import pkxImage0 from '@/assets/images/商城PC端4（无字）_110.jpg'
import pkxImage1 from '@/assets/images/商城PC端4（无字）_112.jpg'
import pkxImage2 from '@/assets/images/商城PC端4（无字）_114.jpg'
import pkxImage3 from '@/assets/images/商城PC端4（无字）_119.jpg'
import pkxImage4 from '@/assets/images/商城PC端4（无字）_120.jpg'
import pkxImage5 from '@/assets/images/商城PC端4（无字）_121.jpg'

const pkxProductList = ref([
  {
    title: '茶陆壹号',
    desc: '这款茶甄选原产地-苍梧县六堡镇的1997年原品种一级茶叶',
    amount: 58,
    image: pkxImage0
  },
  {
    title: '茶陆壹号',
    desc: '这款茶甄选原产地-苍梧县六堡镇的1997年原品种一级茶叶',
    amount: 58,
    image: pkxImage1
  },
  {
    title: '茶陆壹号',
    desc: '这款茶甄选原产地-苍梧县六堡镇的1997年原品种一级茶叶',
    amount: 58,
    image: pkxImage2
  },
  {
    title: '茶陆壹号',
    desc: '这款茶甄选原产地-苍梧县六堡镇的1997年原品种一级茶叶',
    amount: 58,
    image: pkxImage3
  },
  {
    title: '茶陆壹号',
    desc: '这款茶甄选原产地-苍梧县六堡镇的1997年原品种一级茶叶',
    amount: 58,
    image: pkxImage4
  },
  {
    title: '茶陆壹号',
    desc: '这款茶甄选原产地-苍梧县六堡镇的1997年原品种一级茶叶',
    amount: 58,
    image: pkxImage5
  }

])

// 特色茶叶产品数据
const specialTeaProducts = ref([
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶'
  },
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶'
  },
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶'
  }
])

</script>

<style scoped>
.home-container {
  width: 100%;
}

/* 产品展示区域 */
.product-showcase {
  margin-top: 0;
  padding: 0;
}

.showcase-content {
  display: flex;
  min-height: 500px;
}

/* 左侧分类菜单 */
.category-menu {
  width: 200px;
  background-color: #1c705e;
  padding: 0;
  flex-shrink: 0;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.category-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.category-name {
  font-size: 14px;
  font-weight: 500;
}

.category-arrow {
  font-size: 12px;
  opacity: 0.8;
}

/* 右侧产品展示 */
.product-display {
  flex: 1;
  background: url("@/assets/images/商城PC端4（无字）_03.jpg") no-repeat;
  background-size: cover;
  position: relative;
}

.product-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 40px 60px;
}

.product-text {
  flex: 1;
  padding-right: 40px;
}

.product-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  line-height: 1.2;
}

.product-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.5;
}

.order-btn {
  background-color: white;
  color: black;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.order-btn:hover {
  background-color: #459a7e;
}

.product-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.product-image img {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
}

/* 茶叶产品展示区域 */
.tea-products-section {
}

.tea-products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.tea-product-card {
  height: 400px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.tea-product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.tea-product-content {
  padding: 25px;
  color: white;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
}

.tea-product-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 12px;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.tea-product-desc {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 20px;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.tea-product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tea-product-price {
  font-size: 18px;
  font-weight: bold;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.tea-product-btn {
  background-color: white;
  color: #52a58a;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.tea-product-btn:hover {
  background-color: #f0f0f0;
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* 茶叶产品横幅展示模块 */
.tea-banner-section {
  width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.tea-banner-container {
  width: 100%;
  height: 400px;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-radius: 12px;
}

.tea-banner-content {
  padding: 60px 80px;
  max-width: 500px;
}

.tea-banner-text {
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.tea-banner-title {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 20px;
  line-height: 1.2;
}

.tea-banner-subtitle {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 25px;
  opacity: 0.95;
}

.tea-banner-price {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 30px;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.tea-banner-btn {
  background-color: white;
  color: #666666;
  border: none;
  padding: 15px 35px;
  border-radius: 30px;
  font-size: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.tea-banner-btn:hover {
  background-color: #52a58a;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.tea-banner-section-pk {
  width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .tea-banner-container{
    height: 100%;
    margin-bottom: 10px;
    flex: 0 0 300px;
  }

  .tea-banner-content {
    height: 100%;
  }

  .product-list {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;

    .item {
      width: 31%;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin: 0 10px 10px 0;
      background-color: white;

      .content-wrap {
        padding: 20px;
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        flex-direction: column;
        width: 100%;

        H3 {
          padding: 0;
          margin: 0;
        }

        .desc{
          font-size: 14px;
          color: #999;
          line-height: 1.5;
          margin-bottom: 10px;
        }

        .price {
          font-size: 14px;
          color: #333;
        }

        .buy-text {
          font-size: 14px;
          color: #1c705e;
        }
      }
    }
  }
}


/* 产品展示区域样式 */
.products-showcase-section {
  width: 100%;
  margin: 10px 0;
  padding: 0 20px;
}

.products-showcase-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  padding: 0 20px;
}

.product-showcase-item {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
}

.product-showcase-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.product-showcase-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
}

.product-showcase-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-showcase-item:hover .product-showcase-image img {
  transform: scale(1.05);
}

.product-showcase-content {
  padding: 15px;
}

.product-showcase-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.3;
}

.product-showcase-desc {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  margin-bottom: 15px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-showcase-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-showcase-price {
  font-size: 16px;
  font-weight: bold;
  color: #666;
}

.product-showcase-cart-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.product-showcase-cart-btn:hover {
  background-color: #f0f0f0;
}

.product-showcase-cart-btn img {
  width: 20px;
  height: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tea-products-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 0 15px;
  }

  .tea-product-card {
    height: 350px;
  }

  .tea-product-content {
    padding: 20px;
  }

  .tea-product-title {
    font-size: 18px;
  }

  .tea-product-desc {
    font-size: 13px;
  }

  .tea-product-price {
    font-size: 16px;
  }

  .tea-banner-container {
    height: 300px;
  }

  .tea-banner-content {
    padding: 40px 30px;
  }

  .tea-banner-title {
    font-size: 28px;
  }

  .tea-banner-subtitle {
    font-size: 14px;
  }

  .tea-banner-price {
    font-size: 20px;
  }

  .tea-banner-btn {
    padding: 12px 25px;
    font-size: 14px;
  }

  .products-showcase-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
  }

  .product-showcase-image {
    height: 150px;
  }

  .product-showcase-content {
    padding: 12px;
  }

  .product-showcase-title {
    font-size: 14px;
  }

  .product-showcase-desc {
    font-size: 11px;
  }

  .product-showcase-price {
    font-size: 14px;
  }

  .product-showcase-cart-btn img {
    width: 18px;
    height: 18px;
  }
}

/* 横幅样式 */
.banner {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-text h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

/* 优惠券区域 */
.coupon-section {
  margin: 30px auto;
}

.coupon-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.coupon-item {
  background-color: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.coupon-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #1e8e3e;
}

.coupon-desc {
  margin: 8px 0;
  color: #666;
}

.coupon-btn {
  margin-top: 10px;
}

/* 产品区域通用样式 */
.product-section, .category-section {
  margin: 40px auto 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.section-header h2 {
  font-size: 1.8rem;
  color: #333;
  margin: 0;
}

.more-btn {
  color: #666;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.product-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.product-item:hover {
  transform: translateY(-5px);
}

.product-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.product-info {
  padding: 15px;
}

.product-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.product-price {
  color: #e53935;
  font-weight: bold;
}

/* 分类区域特殊样式 */
.category-content {
  display: grid;
  grid-template-columns: 1fr 3fr;
  gap: 20px;
}

.category-banner {
  position: relative;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.category-banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-banner-text {
  position: absolute;
  bottom: 20px;
  left: 20px;
  color: white;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}

.category-banner-text h3 {
  margin-bottom: 10px;
}

/* 特色茶叶展示区域 */
.special-tea-section {
  padding: 60px 0;
  background-color: #f8f9fa;
}

.special-tea-content {
  display: flex;
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;

  .tea-product-card {
    flex: 1;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    box-shadow: none;
  }

  .tea-product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }

  .product-image-container {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    transition: transform 0.3s ease;
  }

  .tea-product-card:hover .product-image-container {
    transform: scale(1.02);
  }

  .product-info {
    color: white;
    padding: 40px 25px 25px;
    text-align: center;
    width: 100%;
  }

  .product-title {
    font-size: 20px;
    font-weight: 600;
    color: white;
    margin-bottom: 12px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .product-description {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
}

/* 页脚样式 */
.footer {
  background-color: #f5f5f5;
  padding: 40px 0 20px;
  margin-top: 60px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.footer-section h4 {
  margin-bottom: 15px;
  color: #333;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 8px;
  color: #666;
  cursor: pointer;
}

.footer-section li:hover {
  color: #1e8e3e;
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons i {
  font-size: 24px;
  color: #666;
  cursor: pointer;
}

.social-icons i:hover {
  color: #1e8e3e;
}

.copyright {
  margin-top: 30px;
  text-align: center;
  color: #999;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .product-grid, .coupon-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .products-showcase-container {
    grid-template-columns: repeat(4, 1fr);
  }

  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }

  .special-tea-content {
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .showcase-content {
    flex-direction: column;
    min-height: auto;
  }

  .category-menu {
    width: 100%;
    display: flex;
    overflow-x: auto;
    background-color: #52a58a;
    padding: 10px 0;
  }

  .category-item {
    white-space: nowrap;
    padding: 10px 15px;
    border-bottom: none;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 120px;
    justify-content: center;
  }

  .category-item:last-child {
    border-right: none;
  }

  .category-arrow {
    display: none;
  }

  .product-content {
    flex-direction: column;
    padding: 30px 20px;
    text-align: center;
  }

  .product-text {
    padding-right: 0;
    margin-bottom: 30px;
  }

  .product-title {
    font-size: 24px;
  }

  .product-subtitle {
    font-size: 14px;
  }

  .product-grid, .coupon-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .products-showcase-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .category-content {
    grid-template-columns: 1fr;
  }

  .category-banner {
    height: 200px;
  }

  .special-tea-content {
    flex-direction: column;
    gap: 15px;
  }

  .product-image-container {
    height: 280px;
  }
}

@media (max-width: 480px) {
  .category-menu {
    padding: 8px 0;
  }

  .category-item {
    padding: 8px 12px;
    min-width: 100px;
    font-size: 13px;
  }

  .product-content {
    padding: 20px 15px;
  }

  .product-title {
    font-size: 20px;
  }

  .product-subtitle {
    font-size: 13px;
    margin-bottom: 20px;
  }

  .order-btn {
    padding: 10px 25px;
    font-size: 14px;
  }

  .product-grid, .coupon-grid {
    grid-template-columns: 1fr;
  }

  .products-showcase-container {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .footer-content {
    grid-template-columns: 1fr;
  }

  .special-tea-section {
    padding: 40px 0;
  }

  .product-info {
    padding: 20px;
  }

  .product-title {
    font-size: 18px;
  }

  .product-description {
    font-size: 13px;
  }

  .product-image-container {
    height: 250px;
  }

  .product-info {
    padding: 30px 20px 20px;
  }
}
</style>