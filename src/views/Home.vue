<template>
  <div class="home-container">
    <Header :scroll="scroll"></Header>

    <!-- 产品展示区域 -->
    <div class="product-showcase">
      <div class="container">
        <div class="showcase-content">
          <!-- 左侧产品分类菜单 -->
          <div class="category-menu">
            <div class="category-item" v-for="(category, index) in productCategories" :key="index">
              <span class="category-name">{{ category.name }}</span>
              <span class="category-arrow">></span>
            </div>
          </div>

          <!-- 右侧产品展示 -->
          <el-carousel height="533px" style="width: 1160px;">
            <el-carousel-item style="height: 533px; width: 1160px">
              <img src="@/assets/images/商城PC端4(有字)_03.jpg" alt="" style="height: 533px; width: 1160px">
            </el-carousel-item>
            <el-carousel-item style="height: 533px; width: 1160px">
              <img src="@/assets/images/商城PC端4(有字)_031.png" alt="" style="height: 533px; width: 1160px">
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </div>

    <div class="flex justify-around items-center mx-auto mt-1" style="max-width: 1200px; margin-top: 1rem">
      <img :src="item" alt="" v-for="(item, index) in introImages" :key="index" style="flex: 0 0 260px;width: 260px"
           class="rounded">
    </div>

    <div class="flex justify-around items-center mx-auto mt-1" style="max-width: 1200px; margin-top: 1rem">
      <img :src="item" alt="" v-for="(item, index) in couponImages" :key="index"
           style="flex: 0 0 260px;width: 260px; object-fit: cover"
           class="rounded">
    </div>

    <!-- 茶叶产品展示区域 -->
    <div class="product-section container">
      <div class="section-header">
        <h2>活动热卖</h2>
        <el-button type="text" class="more-btn">查看更多 >></el-button>
      </div>
    </div>
    <div class="tea-products-section">
      <div class="container">
        <div class="tea-products-grid">
          <div class="tea-product-card" v-for="(product, index) in teaProducts" :key="index"
               :style="{ backgroundImage: `url(${product.backgroundImage})` }">
            <div class="tea-product-content">
              <h3 class="tea-product-title">{{ product.title }}</h3>
              <p class="tea-product-desc">{{ product.description }}</p>
              <div class="tea-product-footer">
                <span class="tea-product-price">{{ product.price }}</span>
                <button class="tea-product-btn">立即购买</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新品上市 -->
    <div class="product-section container">
      <div class="section-header">
        <h2>新品上市</h2>
        <el-button type="text" class="more-btn">查看更多 >></el-button>
      </div>
    </div>
    <div class="tea-products-section">
      <div class="container">
        <div class="tea-products-grid">
          <div class="tea-product-card" v-for="(product, index) in newProducts" :key="index"
               :style="{ backgroundImage: `url(${product.backgroundImage})` }">
            <div class="tea-product-content">
              <h3 class="tea-product-title">{{ product.title }}</h3>
              <p class="tea-product-desc">{{ product.description }}</p>
              <div class="tea-product-footer">
                <span class="tea-product-price">{{ product.price }}</span>
                <button class="tea-product-btn">立即购买</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 茶叶产品横幅展示模块 -->
    <div class="tea-banner-section">
      <div class="tea-banner-container">
        <div class="tea-banner-content">
          <div class="tea-banner-text">
            <h2 class="tea-banner-title">茶陆壹号</h2>
            <p class="tea-banner-subtitle">这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶</p>
            <div class="tea-banner-price">¥58元/盒</div>
            <button class="tea-banner-btn">立即购买</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>关于我们</h4>
            <ul>
              <li>公司简介</li>
              <li>联系方式</li>
              <li>招贤纳士</li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>客户服务</h4>
            <ul>
              <li>配送方式</li>
              <li>支付方式</li>
              <li>售后服务</li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>商家合作</h4>
            <ul>
              <li>入驻条件</li>
              <li>合作方式</li>
              <li>商家中心</li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>关注我们</h4>
            <div class="social-icons">
              <i class="el-icon-s-custom"></i>
              <i class="el-icon-s-promotion"></i>
              <i class="el-icon-s-platform"></i>
            </div>
          </div>
        </div>
        <div class="copyright">
          © 2023 茶叶商城 版权所有
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted, computed} from 'vue'
import Header from '@/components/Header.vue'

const scroll = ref(false)
window.addEventListener('scroll', () => {
  // 顶部
  scroll.value = window.scrollY > 0
})

import introImage0 from '@/assets/images/intro/0.png'
import introImage1 from '@/assets/images/intro/1.png'
import introImage2 from '@/assets/images/intro/2.png'
import introImage3 from '@/assets/images/intro/3.png'

const introImages = [introImage0, introImage1, introImage2, introImage3]

import couponImage0 from '@/assets/images/coupon/30y.png'
import couponImage1 from '@/assets/images/coupon/100y.png'
import couponImage2 from '@/assets/images/coupon/1000y.png'

const couponImages = [couponImage0, couponImage1, couponImage2, couponImage2]

// 导入茶叶产品背景图片
import teaProduct1 from '@/assets/images/商城PC端4（无字）_35.jpg'
import teaProduct2 from '@/assets/images/商城PC端4（无字）_37.jpg'
import teaProduct3 from '@/assets/images/商城PC端4（无字）_39.jpg'

// 茶叶产品数据
const teaProducts = ref([
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
    price: '¥58元/盒',
    backgroundImage: teaProduct1
  },
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
    price: '¥58元/盒',
    backgroundImage: teaProduct2
  },
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
    price: '¥58元/盒',
    backgroundImage: teaProduct3
  }
])

import newProduct1 from '@/assets/images/商城PC端4（无字）_47.jpg'
import newProduct2 from '@/assets/images/商城PC端4（无字）_49.jpg'
import newProduct3 from '@/assets/images/商城PC端4（无字）_51.jpg'
// 茶叶产品数据
const newProducts = ref([
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
    price: '¥58元/盒',
    backgroundImage: newProduct1
  },
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
    price: '¥58元/盒',
    backgroundImage: newProduct2
  },
  {
    title: '茶陆壹号',
    description: '这款茶叶选自优质产地，老茶师精心调制95年制品经典一级茶叶',
    price: '¥58元/盒',
    backgroundImage: newProduct3
  }
])

// 产品分类数据
const productCategories = ref([
  {name: '片刻闲系列'},
  {name: '及时雨系列'},
  {name: '盼秋叶系列'},
  {name: '听风闲香系列'},
  {name: '具象美物系列'},
  {name: '三原系列'},
  {name: '特色系列'},
  {name: '人文旅系列'},
  {name: '传统节庆系列'},
  {name: '经典系列'}
])

// 优惠券数据
const coupons = ref([
  {value: '¥30', desc: '满300元可用'},
  {value: '¥100', desc: '满1000元可用'},
  {value: '¥200', desc: '满2000元可用'},
  {value: '¥1000', desc: '满10000元可用'}
])

// 精选礼盒数据
const giftBoxes = ref([
  {title: '高档礼盒装', price: '688', image: 'https://picsum.photos/300/300?random=1'},
  {title: '精品茶叶礼盒', price: '888', image: 'https://picsum.photos/300/300?random=2'},
  {title: '尊享茶礼', price: '1288', image: 'https://picsum.photos/300/300?random=3'}
])


</script>

<style scoped>
.home-container {
  width: 100%;
}

/* 产品展示区域 */
.product-showcase {
  margin-top: 0;
  padding: 0;
}

.showcase-content {
  display: flex;
  min-height: 500px;
}

/* 左侧分类菜单 */
.category-menu {
  width: 200px;
  background-color: #1c705e;
  padding: 0;
  flex-shrink: 0;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.category-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.category-name {
  font-size: 14px;
  font-weight: 500;
}

.category-arrow {
  font-size: 12px;
  opacity: 0.8;
}

/* 右侧产品展示 */
.product-display {
  flex: 1;
  background: url("@/assets/images/商城PC端4（无字）_03.jpg") no-repeat;
  background-size: cover;
  position: relative;
}

.product-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 40px 60px;
}

.product-text {
  flex: 1;
  padding-right: 40px;
}

.product-title {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  line-height: 1.2;
}

.product-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.5;
}

.order-btn {
  background-color: white;
  color: black;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.order-btn:hover {
  background-color: #459a7e;
}

.product-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.product-image img {
  max-width: 100%;
  max-height: 400px;
  object-fit: contain;
}

/* 茶叶产品展示区域 */
.tea-products-section {
}

.tea-products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.tea-product-card {
  height: 400px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.tea-product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.tea-product-content {
  padding: 25px;
  color: white;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
}

.tea-product-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 12px;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.tea-product-desc {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 20px;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.tea-product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tea-product-price {
  font-size: 18px;
  font-weight: bold;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.tea-product-btn {
  background-color: white;
  color: #52a58a;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.tea-product-btn:hover {
  background-color: #f0f0f0;
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tea-products-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 0 15px;
  }

  .tea-product-card {
    height: 350px;
  }

  .tea-product-content {
    padding: 20px;
  }

  .tea-product-title {
    font-size: 18px;
  }

  .tea-product-desc {
    font-size: 13px;
  }

  .tea-product-price {
    font-size: 16px;
  }
}

/* 横幅样式 */
.banner {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-text h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

/* 优惠券区域 */
.coupon-section {
  margin: 30px auto;
}

.coupon-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.coupon-item {
  background-color: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.coupon-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #1e8e3e;
}

.coupon-desc {
  margin: 8px 0;
  color: #666;
}

.coupon-btn {
  margin-top: 10px;
}

/* 产品区域通用样式 */
.product-section, .category-section {
  margin: 40px auto 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.section-header h2 {
  font-size: 1.8rem;
  color: #333;
  margin: 0;
}

.more-btn {
  color: #666;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.product-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.product-item:hover {
  transform: translateY(-5px);
}

.product-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.product-info {
  padding: 15px;
}

.product-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.product-price {
  color: #e53935;
  font-weight: bold;
}

/* 分类区域特殊样式 */
.category-content {
  display: grid;
  grid-template-columns: 1fr 3fr;
  gap: 20px;
}

.category-banner {
  position: relative;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.category-banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-banner-text {
  position: absolute;
  bottom: 20px;
  left: 20px;
  color: white;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}

.category-banner-text h3 {
  margin-bottom: 10px;
}

/* 页脚样式 */
.footer {
  background-color: #f5f5f5;
  padding: 40px 0 20px;
  margin-top: 60px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.footer-section h4 {
  margin-bottom: 15px;
  color: #333;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 8px;
  color: #666;
  cursor: pointer;
}

.footer-section li:hover {
  color: #1e8e3e;
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons i {
  font-size: 24px;
  color: #666;
  cursor: pointer;
}

.social-icons i:hover {
  color: #1e8e3e;
}

.copyright {
  margin-top: 30px;
  text-align: center;
  color: #999;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .product-grid, .coupon-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .showcase-content {
    flex-direction: column;
    min-height: auto;
  }

  .category-menu {
    width: 100%;
    display: flex;
    overflow-x: auto;
    background-color: #52a58a;
    padding: 10px 0;
  }

  .category-item {
    white-space: nowrap;
    padding: 10px 15px;
    border-bottom: none;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 120px;
    justify-content: center;
  }

  .category-item:last-child {
    border-right: none;
  }

  .category-arrow {
    display: none;
  }

  .product-content {
    flex-direction: column;
    padding: 30px 20px;
    text-align: center;
  }

  .product-text {
    padding-right: 0;
    margin-bottom: 30px;
  }

  .product-title {
    font-size: 24px;
  }

  .product-subtitle {
    font-size: 14px;
  }

  .product-grid, .coupon-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .category-content {
    grid-template-columns: 1fr;
  }

  .category-banner {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .category-menu {
    padding: 8px 0;
  }

  .category-item {
    padding: 8px 12px;
    min-width: 100px;
    font-size: 13px;
  }

  .product-content {
    padding: 20px 15px;
  }

  .product-title {
    font-size: 20px;
  }

  .product-subtitle {
    font-size: 13px;
    margin-bottom: 20px;
  }

  .order-btn {
    padding: 10px 25px;
    font-size: 14px;
  }

  .product-grid, .coupon-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
  }
}
</style>