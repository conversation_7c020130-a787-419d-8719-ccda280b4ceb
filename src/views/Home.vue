<template>
  <div class="home-container">
    <Header></Header>
    <!-- 顶部横幅 -->
    <div class="banner">
      <img src="https://picsum.photos/1200/300" alt="茶叶商城" class="banner-image"/>
      <div class="banner-text">
        <h1>臻藏好茶 · 馨香生活</h1>
        <p>品味人生，品味好茶</p>
      </div>
    </div>

    <!-- 优惠券区域 -->
    <div class="coupon-section container">
      <div class="coupon-grid">
        <div class="coupon-item" v-for="(coupon, index) in coupons" :key="index">
          <div class="coupon-value">{{ coupon.value }}</div>
          <div class="coupon-desc">{{ coupon.desc }}</div>
          <el-button size="small" type="success" class="coupon-btn">领取</el-button>
        </div>
      </div>
    </div>

    <!-- 精选礼盒 -->
    <div class="product-section container">
      <div class="section-header">
        <h2>精选礼盒</h2>
        <el-button type="text" class="more-btn">更多 ></el-button>
      </div>
      <div class="product-grid">
        <div class="product-item" v-for="(item, index) in giftBoxes" :key="index">
          <img :src="item.image" :alt="item.title" class="product-image"/>
          <div class="product-info">
            <div class="product-title">{{ item.title }}</div>
            <div class="product-price">¥{{ item.price }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 茶叶分类 -->
    <div class="category-section container" v-for="(category, idx) in categories" :key="idx">
      <div class="section-header">
        <h2>{{ category.name }}</h2>
        <el-button type="text" class="more-btn">更多 ></el-button>
      </div>
      <div class="category-content">
        <div class="category-banner">
          <img :src="category.banner" :alt="category.name" class="category-banner-image"/>
          <div class="category-banner-text">
            <h3>{{ category.slogan }}</h3>
            <el-button size="small" type="success">查看详情</el-button>
          </div>
        </div>
        <div class="product-grid">
          <div class="product-item" v-for="(item, index) in category.products" :key="index">
            <img :src="item.image" :alt="item.title" class="product-image"/>
            <div class="product-info">
              <div class="product-title">{{ item.title }}</div>
              <div class="product-price">¥{{ item.price }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>关于我们</h4>
            <ul>
              <li>公司简介</li>
              <li>联系方式</li>
              <li>招贤纳士</li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>客户服务</h4>
            <ul>
              <li>配送方式</li>
              <li>支付方式</li>
              <li>售后服务</li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>商家合作</h4>
            <ul>
              <li>入驻条件</li>
              <li>合作方式</li>
              <li>商家中心</li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>关注我们</h4>
            <div class="social-icons">
              <i class="el-icon-s-custom"></i>
              <i class="el-icon-s-promotion"></i>
              <i class="el-icon-s-platform"></i>
            </div>
          </div>
        </div>
        <div class="copyright">
          © 2023 茶叶商城 版权所有
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted, computed} from 'vue'
import Header from '@/components/Header.vue'
// 优惠券数据
const coupons = ref([
  { value: '¥30', desc: '满300元可用' },
  { value: '¥100', desc: '满1000元可用' },
  { value: '¥200', desc: '满2000元可用' },
  { value: '¥1000', desc: '满10000元可用' }
])

// 精选礼盒数据
const giftBoxes = ref([
  { title: '高档礼盒装', price: '688', image: 'https://picsum.photos/300/300?random=1' },
  { title: '精品茶叶礼盒', price: '888', image: 'https://picsum.photos/300/300?random=2' },
  { title: '尊享茶礼', price: '1288', image: 'https://picsum.photos/300/300?random=3' }
])

// 茶叶分类数据
const categories = ref([
  {
    name: '乌龙茶',
    slogan: '韵味悠长 回甘持久',
    banner: 'https://picsum.photos/600/300?random=4',
    products: [
      { title: '铁观音', price: '298', image: 'https://picsum.photos/300/300?random=5' },
      { title: '大红袍', price: '398', image: 'https://picsum.photos/300/300?random=6' },
      { title: '武夷岩茶', price: '498', image: 'https://picsum.photos/300/300?random=7' },
      { title: '凤凰单丛', price: '598', image: 'https://picsum.photos/300/300?random=8' }
    ]
  },
  {
    name: '绿茶',
    slogan: '清新雅致 回甘爽口',
    banner: 'https://picsum.photos/600/300?random=9',
    products: [
      { title: '西湖龙井', price: '368', image: 'https://picsum.photos/300/300?random=10' },
      { title: '碧螺春', price: '298', image: 'https://picsum.photos/300/300?random=11' },
      { title: '信阳毛尖', price: '258', image: 'https://picsum.photos/300/300?random=12' },
      { title: '黄山毛峰', price: '328', image: 'https://picsum.photos/300/300?random=13' }
    ]
  },
  {
    name: '红茶',
    slogan: '醇厚甘甜 暖心暖胃',
    banner: 'https://picsum.photos/600/300?random=14',
    products: [
      { title: '正山小种', price: '268', image: 'https://picsum.photos/300/300?random=15' },
      { title: '祁门红茶', price: '298', image: 'https://picsum.photos/300/300?random=16' },
      { title: '滇红', price: '238', image: 'https://picsum.photos/300/300?random=17' },
      { title: '金骏眉', price: '368', image: 'https://picsum.photos/300/300?random=18' }
    ]
  },
  {
    name: '茶具',
    slogan: '匠心工艺 品茗必备',
    banner: 'https://picsum.photos/600/300?random=19',
    products: [
      { title: '紫砂壶套装', price: '1288', image: 'https://picsum.photos/300/300?random=20' },
      { title: '功夫茶具', price: '688', image: 'https://picsum.photos/300/300?random=21' },
      { title: '陶瓷茶杯', price: '198', image: 'https://picsum.photos/300/300?random=22' }
    ]
  }
])
</script>

<style scoped>
.home-container {
  width: 100%;
}

/* 横幅样式 */
.banner {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-text h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

/* 优惠券区域 */
.coupon-section {
  margin: 30px auto;
}

.coupon-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.coupon-item {
  background-color: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.coupon-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #1e8e3e;
}

.coupon-desc {
  margin: 8px 0;
  color: #666;
}

.coupon-btn {
  margin-top: 10px;
}

/* 产品区域通用样式 */
.product-section, .category-section {
  margin: 40px auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 1.8rem;
  color: #333;
  margin: 0;
}

.more-btn {
  color: #1e8e3e;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.product-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.product-item:hover {
  transform: translateY(-5px);
}

.product-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.product-info {
  padding: 15px;
}

.product-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.product-price {
  color: #e53935;
  font-weight: bold;
}

/* 分类区域特殊样式 */
.category-content {
  display: grid;
  grid-template-columns: 1fr 3fr;
  gap: 20px;
}

.category-banner {
  position: relative;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.category-banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-banner-text {
  position: absolute;
  bottom: 20px;
  left: 20px;
  color: white;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
}

.category-banner-text h3 {
  margin-bottom: 10px;
}

/* 页脚样式 */
.footer {
  background-color: #f5f5f5;
  padding: 40px 0 20px;
  margin-top: 60px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.footer-section h4 {
  margin-bottom: 15px;
  color: #333;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 8px;
  color: #666;
  cursor: pointer;
}

.footer-section li:hover {
  color: #1e8e3e;
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons i {
  font-size: 24px;
  color: #666;
  cursor: pointer;
}

.social-icons i:hover {
  color: #1e8e3e;
}

.copyright {
  margin-top: 30px;
  text-align: center;
  color: #999;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .product-grid, .coupon-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .product-grid, .coupon-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .category-content {
    grid-template-columns: 1fr;
  }
  
  .category-banner {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .product-grid, .coupon-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
  }
}
</style>