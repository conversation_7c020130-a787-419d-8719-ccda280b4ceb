<template>
  <div class="address-page">
    <Header :scroll="scroll"/>

    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="container">
        <h1 class="page-title">收货地址管理</h1>
        <p class="page-subtitle">管理您的收货地址信息</p>
      </div>
    </div>

    <!-- 地址内容区域 -->
    <div class="address-content">
      <div class="container">
        <!-- 添加地址按钮 -->
        <div class="add-address-section">
          <button class="add-address-btn" @click="showAddDialog">
            <span class="add-icon">+</span>
            添加新地址
          </button>
        </div>

        <!-- 地址列表 -->
        <div class="address-list">
          <div
            v-for="(address, index) in addresses"
            :key="address.id"
            class="address-card"
            :class="{ 'default': address.isDefault }"
          >
            <!-- 地址信息 -->
            <div class="address-info">
              <div class="address-header">
                <div class="recipient-info">
                  <span class="recipient-name">{{ address.name }}</span>
                  <span class="recipient-phone">{{ address.phone }}</span>
                </div>
                <div class="address-tags">
                  <span v-if="address.isDefault" class="default-tag">默认</span>
                  <span class="address-tag">{{ address.tag }}</span>
                </div>
              </div>
              <div class="address-detail">
                <p class="address-text">
                  {{ address.province }}{{ address.city }}{{ address.district }}{{ address.detail }}
                </p>
              </div>
            </div>

            <!-- 地址操作 -->
            <div class="address-actions">
              <button
                v-if="!address.isDefault"
                class="action-btn default-btn"
                @click="setDefault(address.id)"
              >
                设为默认
              </button>
              <button
                class="action-btn edit-btn"
                @click="editAddress(address)"
              >
                编辑
              </button>
              <button
                class="action-btn delete-btn"
                @click="deleteAddress(address.id)"
              >
                删除
              </button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="addresses.length === 0" class="empty-state">
          <div class="empty-icon">
            <img src="@/assets/images/cart-green.png" alt="暂无地址" class="empty-image">
          </div>
          <p class="empty-text">您还没有添加收货地址</p>
          <p class="empty-desc">添加收货地址，让购物更便捷</p>
          <button class="add-first-btn" @click="showAddDialog">添加地址</button>
        </div>
      </div>
    </div>

    <!-- 添加/编辑地址弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑地址' : '添加地址'"
      width="500px"
      class="address-dialog"
    >
      <el-form
        ref="addressFormRef"
        :model="addressForm"
        :rules="addressRules"
        label-width="80px"
      >
        <el-form-item label="收货人" prop="name">
          <el-input v-model="addressForm.name" placeholder="请输入收货人姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="addressForm.phone" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item label="所在地区" prop="region">
          <el-cascader
            v-model="addressForm.region"
            :options="regionOptions"
            placeholder="请选择省市区"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="详细地址" prop="detail">
          <el-input
            v-model="addressForm.detail"
            type="textarea"
            :rows="3"
            placeholder="请输入详细地址（街道、门牌号等）"
          />
        </el-form-item>
        <el-form-item label="地址标签" prop="tag">
          <el-radio-group v-model="addressForm.tag">
            <el-radio label="家">家</el-radio>
            <el-radio label="公司">公司</el-radio>
            <el-radio label="学校">学校</el-radio>
            <el-radio label="其他">其他</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="addressForm.isDefault">设为默认地址</el-checkbox>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAddress">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 页脚 -->
    <Footer/>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'

const router = useRouter()
const scroll = ref(false)

// 监听滚动
window.addEventListener('scroll', () => {
  scroll.value = window.scrollY > 0
})

// 弹窗相关
const dialogVisible = ref(false)
const isEdit = ref(false)
const addressFormRef = ref()

// 地址数据
const addresses = ref([
  {
    id: 1,
    name: '张三',
    phone: '138****8888',
    province: '广西壮族自治区',
    city: '梧州市',
    district: '万秀区',
    detail: '新兴二路32号茶船古道文化产业园',
    tag: '公司',
    isDefault: true
  },
  {
    id: 2,
    name: '李四',
    phone: '139****9999',
    province: '广东省',
    city: '广州市',
    district: '天河区',
    detail: '珠江新城花城大道123号',
    tag: '家',
    isDefault: false
  }
])

// 地址表单
const addressForm = reactive({
  id: null,
  name: '',
  phone: '',
  region: [],
  detail: '',
  tag: '家',
  isDefault: false
})

// 表单验证规则
const addressRules = {
  name: [
    { required: true, message: '请输入收货人姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '姓名长度在 2 到 10 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  region: [
    { required: true, message: '请选择所在地区', trigger: 'change' }
  ],
  detail: [
    { required: true, message: '请输入详细地址', trigger: 'blur' },
    { min: 5, max: 100, message: '详细地址长度在 5 到 100 个字符', trigger: 'blur' }
  ]
}

// 地区选项（简化版，实际项目中应该从API获取）
const regionOptions = ref([
  {
    value: '广西壮族自治区',
    label: '广西壮族自治区',
    children: [
      {
        value: '梧州市',
        label: '梧州市',
        children: [
          { value: '万秀区', label: '万秀区' },
          { value: '长洲区', label: '长洲区' },
          { value: '龙圩区', label: '龙圩区' }
        ]
      }
    ]
  },
  {
    value: '广东省',
    label: '广东省',
    children: [
      {
        value: '广州市',
        label: '广州市',
        children: [
          { value: '天河区', label: '天河区' },
          { value: '越秀区', label: '越秀区' },
          { value: '海珠区', label: '海珠区' }
        ]
      }
    ]
  }
])

// 显示添加地址弹窗
const showAddDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑地址
const editAddress = (address) => {
  isEdit.value = true
  addressForm.id = address.id
  addressForm.name = address.name
  addressForm.phone = address.phone
  addressForm.region = [address.province, address.city, address.district]
  addressForm.detail = address.detail
  addressForm.tag = address.tag
  addressForm.isDefault = address.isDefault
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  addressForm.id = null
  addressForm.name = ''
  addressForm.phone = ''
  addressForm.region = []
  addressForm.detail = ''
  addressForm.tag = '家'
  addressForm.isDefault = false
}

// 保存地址
const saveAddress = async () => {
  try {
    await addressFormRef.value.validate()

    const addressData = {
      name: addressForm.name,
      phone: addressForm.phone,
      province: addressForm.region[0],
      city: addressForm.region[1],
      district: addressForm.region[2],
      detail: addressForm.detail,
      tag: addressForm.tag,
      isDefault: addressForm.isDefault
    }

    if (isEdit.value) {
      // 编辑地址
      const index = addresses.value.findIndex(addr => addr.id === addressForm.id)
      if (index !== -1) {
        addresses.value[index] = { ...addressData, id: addressForm.id }
      }
      ElMessage.success('地址修改成功')
    } else {
      // 添加地址
      const newAddress = {
        ...addressData,
        id: Date.now()
      }
      addresses.value.push(newAddress)
      ElMessage.success('地址添加成功')
    }

    // 如果设置为默认地址，取消其他地址的默认状态
    if (addressForm.isDefault) {
      addresses.value.forEach(addr => {
        if (addr.id !== addressForm.id) {
          addr.isDefault = false
        }
      })
    }

    dialogVisible.value = false
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

// 设为默认地址
const setDefault = (addressId) => {
  addresses.value.forEach(addr => {
    addr.isDefault = addr.id === addressId
  })
  ElMessage.success('默认地址设置成功')
}

// 删除地址
const deleteAddress = (addressId) => {
  ElMessageBox.confirm(
    '确定要删除这个地址吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = addresses.value.findIndex(addr => addr.id === addressId)
    if (index !== -1) {
      addresses.value.splice(index, 1)
      ElMessage.success('地址删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}
</script>

<style scoped>
.address-page {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 页面标题区域 */
.page-header {
  background: linear-gradient(135deg, #52a58a 0%, #459a7e 100%);
  padding: 60px 0;
  color: white;
  text-align: center;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 15px 0;
}

.page-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

/* 地址内容区域 */
.address-content {
  padding: 40px 0;
}

/* 添加地址按钮区域 */
.add-address-section {
  margin-bottom: 30px;
  text-align: center;
}

.add-address-btn {
  background: #52a58a;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 25px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.add-address-btn:hover {
  background: #459a7e;
  transform: translateY(-2px);
}

.add-icon {
  font-size: 20px;
  font-weight: bold;
}

/* 地址列表 */
.address-list {
  display: grid;
  gap: 20px;
}

/* 地址卡片 */
.address-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.address-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.address-card.default {
  border: 2px solid #52a58a;
}

.address-card.default::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #52a58a 0%, #459a7e 100%);
  border-radius: 12px 12px 0 0;
}

/* 地址信息 */
.address-info {
  margin-bottom: 20px;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.recipient-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.recipient-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.recipient-phone {
  font-size: 16px;
  color: #666;
}

.address-tags {
  display: flex;
  gap: 8px;
}

.default-tag {
  background: #52a58a;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.address-tag {
  background: #f0f0f0;
  color: #666;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.address-detail {
  margin-top: 10px;
}

.address-text {
  font-size: 15px;
  color: #555;
  line-height: 1.5;
  margin: 0;
}

/* 地址操作 */
.address-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.action-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 20px;
  background: white;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
}

.default-btn {
  border-color: #52a58a;
  color: #52a58a;
}

.default-btn:hover {
  background: #52a58a;
  color: white;
}

.edit-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.delete-btn:hover {
  border-color: #e53935;
  color: #e53935;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
}

.empty-icon {
  margin-bottom: 30px;
}

.empty-image {
  width: 120px;
  height: 120px;
  opacity: 0.3;
}

.empty-text {
  font-size: 18px;
  color: #333;
  margin-bottom: 10px;
}

.empty-desc {
  font-size: 14px;
  color: #999;
  margin-bottom: 30px;
}

.add-first-btn {
  background: #52a58a;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.add-first-btn:hover {
  background: #459a7e;
}

/* 弹窗样式 */
:deep(.address-dialog) {
  .el-dialog__header {
    background: #f8f9fa;
    padding: 20px 25px;
    margin: 0;
  }

  .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .el-dialog__body {
    padding: 25px;
  }

  .el-dialog__footer {
    padding: 20px 25px;
    background: #f8f9fa;
  }

  .el-button--primary {
    background: #52a58a;
    border-color: #52a58a;
  }

  .el-button--primary:hover {
    background: #459a7e;
    border-color: #459a7e;
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .address-content {
    padding: 30px 0;
  }

  .address-card {
    padding: 20px;
  }

  .recipient-info {
    gap: 12px;
  }

  .recipient-name {
    font-size: 17px;
  }

  .recipient-phone {
    font-size: 15px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 40px 0;
  }

  .page-title {
    font-size: 24px;
  }

  .page-subtitle {
    font-size: 14px;
  }

  .address-content {
    padding: 30px 0;
  }

  .add-address-section {
    margin-bottom: 25px;
    padding: 0 20px;
  }

  .add-address-btn {
    padding: 12px 25px;
    font-size: 15px;
  }

  .address-list {
    gap: 15px;
    padding: 0 20px;
  }

  .address-card {
    padding: 20px;
  }

  .address-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .recipient-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    width: 100%;
  }

  .recipient-name {
    font-size: 16px;
  }

  .recipient-phone {
    font-size: 14px;
  }

  .address-tags {
    align-self: flex-end;
  }

  .address-text {
    font-size: 14px;
  }

  .address-actions {
    flex-wrap: wrap;
    gap: 8px;
    justify-content: flex-start;
  }

  .action-btn {
    flex: 1;
    min-width: calc(33.333% - 6px);
    text-align: center;
    padding: 10px 12px;
    font-size: 13px;
  }

  .empty-state {
    padding: 60px 15px;
  }

  .empty-image {
    width: 100px;
    height: 100px;
  }

  /* 弹窗移动端适配 */
  :deep(.address-dialog) {
    .el-dialog {
      width: 90% !important;
      margin: 5vh auto !important;
    }

    .el-dialog__body {
      padding: 20px;
    }

    .el-form-item__label {
      font-size: 14px;
    }

    .el-input__inner,
    .el-textarea__inner {
      font-size: 14px;
    }

    .el-cascader {
      width: 100%;
    }

    .el-radio {
      margin-right: 15px;
      margin-bottom: 10px;
    }

    .el-radio__label {
      font-size: 14px;
    }

    .dialog-footer {
      display: flex;
      gap: 10px;
    }

    .el-button {
      flex: 1;
    }
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 30px 0;
  }

  .page-title {
    font-size: 20px;
  }

  .page-subtitle {
    font-size: 13px;
  }

  .address-content {
    padding: 20px 0;
  }

  .add-address-section {
    padding: 0 15px;
  }

  .add-address-btn {
    padding: 10px 20px;
    font-size: 14px;
  }

  .address-list {
    padding: 0 15px;
    gap: 12px;
  }

  .address-card {
    padding: 15px;
  }

  .recipient-name {
    font-size: 15px;
  }

  .recipient-phone {
    font-size: 13px;
  }

  .default-tag,
  .address-tag {
    font-size: 11px;
    padding: 3px 6px;
  }

  .address-text {
    font-size: 13px;
  }

  .address-actions {
    gap: 6px;
  }

  .action-btn {
    padding: 8px 10px;
    font-size: 12px;
    border-radius: 15px;
  }

  .empty-state {
    padding: 40px 10px;
  }

  .empty-text {
    font-size: 16px;
  }

  .empty-desc {
    font-size: 13px;
  }

  .add-first-btn {
    padding: 10px 25px;
    font-size: 14px;
  }

  /* 弹窗超小屏适配 */
  :deep(.address-dialog) {
    .el-dialog {
      width: 95% !important;
      margin: 2vh auto !important;
    }

    .el-dialog__header {
      padding: 15px 20px;
    }

    .el-dialog__title {
      font-size: 16px;
    }

    .el-dialog__body {
      padding: 15px;
    }

    .el-form-item {
      margin-bottom: 18px;
    }

    .el-form-item__label {
      font-size: 13px;
      line-height: 1.4;
    }

    .el-input__inner,
    .el-textarea__inner {
      font-size: 13px;
    }

    .el-radio {
      margin-right: 12px;
      margin-bottom: 8px;
    }

    .el-radio__label {
      font-size: 13px;
    }

    .el-checkbox__label {
      font-size: 13px;
    }

    .dialog-footer {
      padding: 15px 20px;
    }

    .el-button {
      padding: 8px 15px;
      font-size: 13px;
    }
  }
}
</style>
