<template>
  <div>
    <Header scroll placeholder></Header>
    <div class="banner-section">
      <div class="banner-container">
        <img src="@/assets/about/gywm_banner.png" alt="供链合作" class="banner-img"
             :class="{ 'mobile-banner': isMobile }">
        <div class="banner-overlay">
          <div class="banner-content">
            <h2 class="banner-title">供链合作</h2>
            <p class="banner-subtitle">SUPPLY CHAIN COOPERATION</p>
          </div>
        </div>
      </div>
      <div class="banner-button-section">
        <div class="btn banner-btn">供链合作</div>
      </div>
    </div>
    <!-- 表单区域 -->
    <div class="form-section">
      <!-- 桌面端表单 -->
      <div v-if="!isMobile" class="desktop-form-container">
        <div class="form-wrapper">
          <div class="form-header">
            <h3 class="form-title">供链合作申请表</h3>
            <p class="form-description">请填写以下信息，我们将尽快与您联系</p>
          </div>

          <el-form
            ref="form"
            :model="formData"
            :label-width="labelWidth"
            class="desktop-form"
            @submit.prevent="submitForm"
          >
            <el-form-item label="1.您寻求合作的意向是：" :error="formErrors.intention">
              <el-radio-group v-model="formData.intention">
                <div class="radio-group-vertical">
                  <el-radio
                    v-for="(item, index) in intentionList"
                    :key="index"
                    :label="item"
                    border
                    class="radio-item"
                  >
                    {{ item }}
                  </el-radio>
                </div>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="2.您寻求合作的品牌是：">
              <el-select v-model="formData.brand" placeholder="请选择" class="full-width">
                <el-option
                  v-for="brand in brandList"
                  :key="brand"
                  :label="brand"
                  :value="brand"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="3.您的姓名：" :error="formErrors.name">
              <el-input v-model="formData.name" placeholder="请输入您的姓名" class="full-width"></el-input>
            </el-form-item>

            <el-form-item label="4.手机号码：" :error="formErrors.phone">
              <el-input v-model="formData.phone" placeholder="请输入您的手机号码" class="full-width"></el-input>
            </el-form-item>
            <el-form-item label="5.您的性别：">
              <el-radio-group v-model="formData.gender">
                <div class="radio-group-horizontal">
                  <el-radio label="男" border class="radio-item">男</el-radio>
                  <el-radio label="女" border class="radio-item">女</el-radio>
                </div>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="6.您所从事的行业：">
              <el-select v-model="formData.industry" placeholder="请选择您的行业" class="full-width">
                <el-option
                  v-for="industry in industryList"
                  :key="industry"
                  :label="industry"
                  :value="industry"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="7.您所在位置：">
              <el-input v-model="formData.location" placeholder="请输入您所在位置" class="full-width"></el-input>
            </el-form-item>
            <el-form-item label="8.您公司或单位名称 / 个体请填写“个人”：">
              <el-input v-model="formData.company" placeholder="个体请填写'个人'" class="full-width"></el-input>
            </el-form-item>
            <el-form-item label="9. 其它说明（如您的品牌公司介绍，提供的产品和服务简介等，非必填项）：">
              <el-input
                v-model="formData.description"
                type="textarea"
                placeholder="如您的品牌公司介绍，提供的产品和服务简介等（非必填项）"
                :rows="4"
                class="full-width"
              ></el-input>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="submitForm" class="submit-btn">提交申请</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 移动端表单 -->
      <div v-else class="mobile-form-container">
        <div class="mobile-form-wrapper">
          <div class="mobile-form-header">
            <h3 class="mobile-form-title">供链合作申请</h3>
            <p class="mobile-form-description">请填写合作信息</p>

            <!-- 进度指示器 -->
            <div class="progress-indicator">
              <div class="progress-step" :class="{ active: formData.intention }">
                <div class="step-number">1</div>
                <div class="step-label">合作意向</div>
              </div>
              <div class="progress-line" :class="{ active: formData.intention && (formData.name || formData.phone) }"></div>
              <div class="progress-step" :class="{ active: formData.name || formData.phone }">
                <div class="step-number">2</div>
                <div class="step-label">基本信息</div>
              </div>
              <div class="progress-line" :class="{ active: formData.name && formData.phone }"></div>
              <div class="progress-step" :class="{ active: formData.name && formData.phone }">
                <div class="step-number">3</div>
                <div class="step-label">合作详情</div>
              </div>
            </div>
          </div>

          <form class="mobile-form" @submit.prevent="submitForm">
            <!-- 合作意向 -->
            <div class="mobile-form-section">
              <h4 class="section-title">合作意向 *</h4>
              <p class="section-hint">请选择您最感兴趣的合作方式</p>
              <div class="mobile-radio-group">
                <div
                  v-for="(item, index) in intentionList"
                  :key="index"
                  class="mobile-radio-item"
                  :class="{ active: formData.intention === item }"
                  @click="formData.intention = item"
                >
                  {{ item }}
                </div>
              </div>
              <span v-if="formErrors.intention" class="mobile-error">{{ formErrors.intention }}</span>
            </div>

            <!-- 基本信息 -->
            <div class="mobile-form-section">
              <h4 class="section-title">基本信息</h4>
              <div class="mobile-input-group">
                <input
                  v-model="formData.name"
                  type="text"
                  placeholder="您的姓名 *"
                  class="mobile-input"
                  :class="{ error: formErrors.name }"
                >
                <span v-if="formErrors.name" class="mobile-error">{{ formErrors.name }}</span>
              </div>

              <div class="mobile-input-group">
                <input
                  v-model="formData.phone"
                  type="tel"
                  placeholder="手机号码 *"
                  class="mobile-input"
                  :class="{ error: formErrors.phone }"
                >
                <span v-if="formErrors.phone" class="mobile-error">{{ formErrors.phone }}</span>
              </div>

              <div class="mobile-select-group">
                <select v-model="formData.gender" class="mobile-select">
                  <option value="">请选择性别</option>
                  <option value="男">男</option>
                  <option value="女">女</option>
                </select>
              </div>
            </div>

            <!-- 合作详情 -->
            <div class="mobile-form-section">
              <h4 class="section-title">合作详情</h4>
              <div class="mobile-select-group">
                <select v-model="formData.brand" class="mobile-select">
                  <option value="">请选择合作品牌</option>
                  <option v-for="brand in brandList" :key="brand" :value="brand">{{ brand }}</option>
                </select>
              </div>

              <div class="mobile-select-group">
                <select v-model="formData.industry" class="mobile-select">
                  <option value="">请选择所从事行业</option>
                  <option v-for="industry in industryList" :key="industry" :value="industry">{{ industry }}</option>
                </select>
              </div>

              <div class="mobile-input-group">
                <input
                  v-model="formData.location"
                  type="text"
                  placeholder="您所在位置"
                  class="mobile-input"
                >
              </div>

              <div class="mobile-input-group">
                <input
                  v-model="formData.company"
                  type="text"
                  placeholder="公司或单位名称（个体请填写'个人'）"
                  class="mobile-input"
                >
              </div>

              <div class="mobile-input-group">
                <div class="textarea-wrapper">
                  <textarea
                    v-model="formData.description"
                    placeholder="其它说明（如品牌公司介绍，产品和服务简介等）"
                    class="mobile-textarea"
                    rows="4"
                    maxlength="500"
                  ></textarea>
                  <div class="char-counter">{{ formData.description.length }}/500</div>
                </div>
              </div>
            </div>

            <!-- 表单完成度 -->
            <div class="form-completeness">
              <div class="completeness-header">
                <span class="completeness-label">表单完成度</span>
                <span class="completeness-percentage">{{ formCompleteness }}%</span>
              </div>
              <div class="completeness-bar">
                <div class="completeness-fill" :style="{ width: formCompleteness + '%' }"></div>
              </div>
            </div>

            <!-- 提交按钮 -->
            <div class="mobile-form-actions">
              <button
                type="submit"
                class="mobile-submit-btn"
                :class="{ 'btn-ready': formCompleteness >= 60 }"
              >
                {{ formCompleteness >= 60 ? '提交合作申请' : `继续填写 (${formCompleteness}%)` }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <Footer></Footer>
    <SettleIn/>
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted, computed} from 'vue'
import {ElMessage} from 'element-plus'

import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import SettleIn from "@/components/SettleIn.vue";

const isMobile = ref(false)
const deviceInfo = ref({
  type: '',
  os: '',
  browser: '',
  screenWidth: 0
})

// 表单数据
const formData = ref({
  intention: '',
  brand: '',
  name: '',
  phone: '',
  gender: '',
  industry: '',
  location: '',
  company: '',
  description: ''
})

// 表单验证状态
const formErrors = ref({})

// 合作意向列表
const intentionList = ['产品代理与加盟', '产品批发/定制', '品牌授权及联名', '原料供应', '产品联合开发', '海外市场合作', '媒体合作', '其他合作']

// 品牌系列列表
const brandList = [
  '片刻闲系列', '及时雨系列', '盼秋叶系列', '听风闲香系列',
  '具象美物系列', '三原系列', '特色系列', '人文旅系列', '传统节庆系列'
]

// 行业列表
const industryList = [
  '茶馆、茶店、茶舍等空间', '茶叶生产厂家、产地基地', '茶饮料及其他茶叶衍生品',
  '外贸及跨境电商', '媒体及其他事业单位', '其他'
]

// 动态标签宽度
const labelWidth = computed(() => isMobile.value ? '100px' : '300px')

// 获取设备信息
const getDeviceInfo = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  const screenWidth = window.innerWidth

  // 检测操作系统
  let os = 'Unknown'
  if (userAgent.includes('android')) os = 'Android'
  else if (userAgent.includes('iphone') || userAgent.includes('ipad')) os = 'iOS'
  else if (userAgent.includes('windows')) os = 'Windows'
  else if (userAgent.includes('mac')) os = 'macOS'
  else if (userAgent.includes('linux')) os = 'Linux'

  // 检测浏览器
  let browser = 'Unknown'
  if (userAgent.includes('chrome')) browser = 'Chrome'
  else if (userAgent.includes('firefox')) browser = 'Firefox'
  else if (userAgent.includes('safari')) browser = 'Safari'
  else if (userAgent.includes('edge')) browser = 'Edge'

  // 检测设备类型
  let type = 'Desktop'
  if (userAgent.includes('mobile') || screenWidth <= 768) type = 'Mobile'
  else if (userAgent.includes('tablet') || (screenWidth > 768 && screenWidth <= 1024)) type = 'Tablet'

  deviceInfo.value = {type, os, browser, screenWidth}

  return {type, os, browser, screenWidth}
}

// 检测是否为移动设备
const checkMobile = () => {
  const {type, screenWidth} = getDeviceInfo()
  const userAgent = navigator.userAgent.toLowerCase()

  // 更精确的移动设备检测
  const mobileKeywords = ['mobile', 'android', 'iphone', 'ipod', 'blackberry', 'windows phone']
  const tabletKeywords = ['ipad', 'tablet']

  const isMobileDevice = mobileKeywords.some(keyword => userAgent.includes(keyword))
  const isTabletDevice = tabletKeywords.some(keyword => userAgent.includes(keyword))
  const isSmallScreen = screenWidth <= 768
  const isMediumScreen = screenWidth > 768 && screenWidth <= 1024

  // 移动设备或小屏幕显示移动端界面
  isMobile.value = isMobileDevice || isSmallScreen || (isTabletDevice && isMediumScreen)
}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
}

// 表单验证
const validateForm = () => {
  let isValid = true
  formErrors.value = {}

  // 验证必填项
  if (!formData.value.intention) {
    formErrors.value.intention = '请选择合作意向'
    isValid = false
  }

  if (!formData.value.name.trim()) {
    formErrors.value.name = '请输入姓名'
    isValid = false
  }

  // 验证手机号
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!formData.value.phone.trim()) {
    formErrors.value.phone = '请输入手机号'
    isValid = false
  } else if (!phoneRegex.test(formData.value.phone)) {
    formErrors.value.phone = '请输入正确的手机号格式'
    isValid = false
  }

  return isValid
}

// 计算表单完成度
const formCompleteness = computed(() => {
  const fields = ['intention', 'name', 'phone', 'brand', 'industry', 'location', 'company']
  const filledFields = fields.filter(field => formData.value[field] && formData.value[field].trim())
  return Math.round((filledFields.length / fields.length) * 100)
})

// 提交表单
const submitForm = () => {
  if (validateForm()) {
    // 这里可以添加实际的提交逻辑
    ElMessage.success('提交成功！我们会尽快联系您')
    console.log('表单数据:', formData.value)

    // 重置表单
    formData.value = {
      intention: '', brand: '', name: '', phone: '', gender: '',
      industry: '', location: '', company: '', description: ''
    }
  } else {
    ElMessage.error('请检查表单信息')
  }
}

// 组件挂载时初始化
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 基础样式 */
.btn {
  background-color: #1f7260;
  border: 1px solid white;
  padding: 10px 30px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.btn:hover {
  background-color: #155a4a;
  transform: translateY(-2px);
}

/* 横幅区域样式 */
.banner-section {
  position: relative;
}

.banner-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.banner-img {
  width: 100%;
  height: 350px;
  object-fit: cover;
}

.mobile-banner {
  height: 250px;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
}

.banner-content {
  text-align: center;
  color: white;
}

.banner-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-subtitle {
  font-size: 1.2rem;
  margin: 10px 0;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.banner-button-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 1rem 0;
}

.banner-btn {
  font-size: 1rem;
  padding: 12px 40px;
}

/* 表单区域样式 */
.form-section {
  background: url("@/assets/contact/lxwm_bj.png");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 100vh;
  padding: 40px 0;
}

/* 桌面端表单样式 */
.desktop-form-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 2rem;
}

.form-wrapper {
  max-width: 900px;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-title {
  color: #1f7260;
  font-size: 2rem;
  font-weight: bold;
  margin: 0 0 10px 0;
}

.form-description {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

.desktop-form {
  margin-top: 20px;
}

.radio-group-vertical {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.radio-group-horizontal {
  display: flex;
  gap: 20px;
}

.radio-item {
  margin-bottom: 10px !important;
  margin-right: 0 !important;
}

.full-width {
  width: 100%;
}

.submit-btn {
  background: linear-gradient(135deg, #1f7260 0%, #27ae60 100%);
  border: none;
  padding: 14px 40px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(31, 114, 96, 0.3);
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(31, 114, 96, 0.4);
}

/* 移动端表单样式 */
.mobile-form-container {
  padding: 20px;
  min-height: 100vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.mobile-form-wrapper {
  width: 100%;
  max-width: 500px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 25px 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.mobile-form-header {
  text-align: center;
  margin-bottom: 25px;
}

.mobile-form-title {
  color: #1f7260;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 0 8px 0;
}

.mobile-form-description {
  color: #666;
  font-size: 0.9rem;
  margin: 0 0 20px 0;
}

/* 进度指示器样式 */
.progress-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  padding: 15px 0;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e0e0e0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.step-label {
  font-size: 10px;
  color: #999;
  text-align: center;
  transition: all 0.3s ease;
}

.progress-step.active .step-number {
  background: #1f7260;
  color: white;
}

.progress-step.active .step-label {
  color: #1f7260;
  font-weight: 600;
}

.progress-line {
  width: 40px;
  height: 2px;
  background: #e0e0e0;
  margin: 0 10px;
  transition: all 0.3s ease;
}

.progress-line.active {
  background: #1f7260;
}

.mobile-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.mobile-form-section {
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(31, 114, 96, 0.1);
}

.section-title {
  color: #1f7260;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(31, 114, 96, 0.2);
}

.section-hint {
  color: #999;
  font-size: 12px;
  margin: 0 0 15px 0;
  line-height: 1.4;
}

.mobile-radio-group {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
}

.mobile-radio-item {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  text-align: center;
}

.mobile-radio-item:hover {
  border-color: #1f7260;
  background: rgba(31, 114, 96, 0.05);
}

.mobile-radio-item.active {
  border-color: #1f7260;
  background: rgba(31, 114, 96, 0.1);
  color: #1f7260;
  font-weight: 600;
}

.mobile-input-group,
.mobile-select-group {
  margin-bottom: 15px;
}

.mobile-input,
.mobile-select,
.mobile-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
  color: #333;
}

.mobile-input:focus,
.mobile-select:focus,
.mobile-textarea:focus {
  outline: none;
  border-color: #1f7260;
  box-shadow: 0 0 0 3px rgba(31, 114, 96, 0.1);
}

.mobile-input.error,
.mobile-select.error,
.mobile-textarea.error {
  border-color: #e74c3c;
  background: rgba(255, 235, 235, 0.5);
}

.mobile-input::placeholder,
.mobile-select::placeholder,
.mobile-textarea::placeholder {
  color: #999;
}

.mobile-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.textarea-wrapper {
  position: relative;
}

.char-counter {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 11px;
  color: #999;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 4px;
  pointer-events: none;
}

.mobile-error {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

.mobile-form-actions {
  margin-top: 10px;
}

.mobile-submit-btn {
  width: 100%;
  background: linear-gradient(135deg, #1f7260 0%, #27ae60 100%);
  color: white;
  border: none;
  padding: 16px 20px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(31, 114, 96, 0.3);
}

.mobile-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(31, 114, 96, 0.4);
}

.mobile-submit-btn:active {
  transform: translateY(0);
}

.mobile-submit-btn.btn-ready {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  box-shadow: 0 4px 15px rgba(39, 174, 96, 0.4);
}

.mobile-submit-btn.btn-ready:hover {
  box-shadow: 0 8px 25px rgba(39, 174, 96, 0.5);
}

/* 表单完成度样式 */
.form-completeness {
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12px;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid rgba(31, 114, 96, 0.1);
}

.completeness-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.completeness-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.completeness-percentage {
  font-size: 14px;
  color: #1f7260;
  font-weight: 600;
}

.completeness-bar {
  width: 100%;
  height: 6px;
  background: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
}

.completeness-fill {
  height: 100%;
  background: linear-gradient(90deg, #1f7260 0%, #27ae60 100%);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-title {
    font-size: 2rem;
  }

  .banner-subtitle {
    font-size: 1rem;
    margin: 8px 0;
  }

  .banner-btn {
    padding: 10px 30px;
    font-size: 0.9rem;
  }

  .form-section {
    padding: 20px 0;
  }

  .mobile-form-wrapper {
    padding: 20px 15px;
  }

  .mobile-form-section {
    padding: 15px;
  }

  .section-title {
    font-size: 1rem;
  }

  .mobile-radio-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .mobile-input,
  .mobile-select,
  .mobile-textarea {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .banner-img {
    height: 200px;
  }

  .banner-title {
    font-size: 1.8rem;
  }

  .banner-subtitle {
    font-size: 0.9rem;
  }

  .banner-btn {
    padding: 8px 20px;
    font-size: 0.8rem;
  }

  .mobile-form-wrapper {
    padding: 15px 10px;
    margin: 0 10px;
  }

  .mobile-form-title {
    font-size: 1.3rem;
  }

  .mobile-form-description {
    font-size: 0.8rem;
  }

  .mobile-form {
    gap: 20px;
  }

  .mobile-form-section {
    padding: 12px;
  }

  .section-title {
    font-size: 0.9rem;
    margin-bottom: 12px;
  }

  .mobile-radio-group {
    gap: 8px;
  }

  .mobile-radio-item {
    padding: 8px 10px;
    font-size: 12px;
  }

  .mobile-input,
  .mobile-select,
  .mobile-textarea {
    padding: 8px 10px;
    font-size: 12px;
  }

  .mobile-submit-btn {
    padding: 14px 16px;
    font-size: 14px;
  }
}
</style>