<template>
  <div class="payment-container">
    <!-- 头部 -->
    <div class="header">
      <div class="header-content">
        <button class="back-btn" @click="goBack">
          <i class="icon-back">←</i>
        </button>
        <h1 class="page-title">支付</h1>
      </div>
    </div>

    <div class="payment-content">
      <!-- 支付金额 -->
      <div class="amount-section">
        <div class="amount-display">
          <span class="currency">¥</span>
          <span class="amount">{{ paymentAmount }}</span>
        </div>
        <p class="order-info">订单号：{{ orderId }}</p>
      </div>

      <!-- 支付方式选择 -->
      <div class="payment-methods">
        <h2 class="section-title">选择支付方式</h2>
        <div class="method-list">
          <div 
            class="method-item" 
            v-for="(method, index) in paymentMethods" 
            :key="index"
            :class="{ active: selectedMethodIndex === index }"
            @click="selectPaymentMethod(index)"
          >
            <div class="method-icon">
              <img :src="method.icon" :alt="method.name" />
            </div>
            <div class="method-info">
              <h3 class="method-name">{{ method.name }}</h3>
              <p class="method-desc">{{ method.description }}</p>
            </div>
            <div class="method-radio">
              <span class="radio" :class="{ checked: selectedMethodIndex === index }"></span>
            </div>
          </div>
        </div>
      </div>

      <!-- 优惠券 -->
      <div class="coupon-section">
        <div class="coupon-item" @click="showCouponModal = true">
          <div class="coupon-icon">🎫</div>
          <div class="coupon-info">
            <span class="coupon-text">优惠券</span>
            <span class="coupon-desc" v-if="selectedCoupon">
              已选择：{{ selectedCoupon.name }} -¥{{ selectedCoupon.discount }}
            </span>
            <span class="coupon-desc" v-else>选择可用优惠券</span>
          </div>
          <div class="coupon-arrow">></div>
        </div>
      </div>

      <!-- 支付详情 -->
      <div class="payment-details">
        <h2 class="section-title">支付详情</h2>
        <div class="detail-list">
          <div class="detail-item">
            <span class="detail-label">商品金额</span>
            <span class="detail-value">¥{{ originalAmount }}</span>
          </div>
          <div class="detail-item" v-if="shippingFee > 0">
            <span class="detail-label">运费</span>
            <span class="detail-value">¥{{ shippingFee }}</span>
          </div>
          <div class="detail-item" v-if="selectedCoupon">
            <span class="detail-label">优惠券</span>
            <span class="detail-value discount">-¥{{ selectedCoupon.discount }}</span>
          </div>
          <div class="detail-item total">
            <span class="detail-label">实付金额</span>
            <span class="detail-value">¥{{ paymentAmount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部支付按钮 -->
    <div class="payment-footer">
      <div class="payment-info">
        <span class="total-label">实付：</span>
        <span class="total-amount">¥{{ paymentAmount }}</span>
      </div>
      <button class="pay-btn" @click="handlePayment" :disabled="!canPay">
        确认支付
      </button>
    </div>

    <!-- 优惠券选择弹窗 -->
    <div class="modal-overlay" v-if="showCouponModal" @click="showCouponModal = false">
      <div class="coupon-modal" @click.stop>
        <div class="modal-header">
          <h3>选择优惠券</h3>
          <button class="close-btn" @click="showCouponModal = false">×</button>
        </div>
        <div class="modal-content">
          <div class="coupon-list">
            <div 
              class="coupon-card" 
              v-for="(coupon, index) in availableCoupons" 
              :key="index"
              :class="{ 
                active: selectedCouponIndex === index,
                disabled: !coupon.available 
              }"
              @click="selectCoupon(index)"
            >
              <div class="coupon-left">
                <div class="coupon-amount">¥{{ coupon.discount }}</div>
                <div class="coupon-condition">{{ coupon.condition }}</div>
              </div>
              <div class="coupon-right">
                <h4 class="coupon-name">{{ coupon.name }}</h4>
                <p class="coupon-validity">{{ coupon.validity }}</p>
                <span class="coupon-status" v-if="!coupon.available">不可用</span>
              </div>
            </div>
            
            <div class="no-coupon-option" 
                 :class="{ active: selectedCouponIndex === -1 }"
                 @click="selectCoupon(-1)">
              <span>不使用优惠券</span>
              <span class="radio" :class="{ checked: selectedCouponIndex === -1 }"></span>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="confirm-btn" @click="confirmCoupon">确认</button>
        </div>
      </div>
    </div>

    <!-- 支付结果弹窗 -->
    <div class="modal-overlay" v-if="showPaymentResult" @click="closePaymentResult">
      <div class="payment-result-modal" @click.stop>
        <div class="result-icon">
          <span v-if="paymentSuccess">✅</span>
          <span v-else>❌</span>
        </div>
        <h3 class="result-title">
          {{ paymentSuccess ? '支付成功' : '支付失败' }}
        </h3>
        <p class="result-message">
          {{ paymentSuccess ? '您的订单已支付成功，我们将尽快为您处理' : '支付过程中出现问题，请重试' }}
        </p>
        <div class="result-actions">
          <button class="result-btn" @click="goToOrders" v-if="paymentSuccess">
            查看订单
          </button>
          <button class="result-btn secondary" @click="closePaymentResult">
            {{ paymentSuccess ? '继续购物' : '重新支付' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 基础数据
const orderId = ref('')
const originalAmount = ref(1066)
const shippingFee = ref(10)

// 支付方式
const selectedMethodIndex = ref(0)
const paymentMethods = ref([
  {
    name: '微信支付',
    description: '推荐使用微信支付',
    icon: '/icons/wechat-pay.png'
  },
  {
    name: '支付宝',
    description: '支持花呗分期付款',
    icon: '/icons/alipay.png'
  },
  {
    name: '银行卡支付',
    description: '支持各大银行储蓄卡及信用卡',
    icon: '/icons/bank-card.png'
  }
])

// 优惠券
const showCouponModal = ref(false)
const selectedCouponIndex = ref(-1)
const selectedCoupon = ref(null)
const availableCoupons = ref([
  {
    name: '新用户专享券',
    discount: 30,
    condition: '满300可用',
    validity: '有效期至2024-12-31',
    available: true
  },
  {
    name: '满减优惠券',
    discount: 100,
    condition: '满1000可用',
    validity: '有效期至2024-12-31',
    available: true
  },
  {
    name: '限时特惠券',
    discount: 50,
    condition: '满500可用',
    validity: '有效期至2024-11-30',
    available: false
  }
])

// 支付结果
const showPaymentResult = ref(false)
const paymentSuccess = ref(false)

// 计算属性
const paymentAmount = computed(() => {
  let amount = originalAmount.value + shippingFee.value
  if (selectedCoupon.value) {
    amount -= selectedCoupon.value.discount
  }
  return Math.max(amount, 0)
})

const canPay = computed(() => {
  return selectedMethodIndex.value >= 0 && paymentAmount.value > 0
})

// 方法
const goBack = () => {
  router.go(-1)
}

const selectPaymentMethod = (index) => {
  selectedMethodIndex.value = index
}

const selectCoupon = (index) => {
  if (index >= 0 && !availableCoupons.value[index].available) {
    return
  }
  selectedCouponIndex.value = index
}

const confirmCoupon = () => {
  if (selectedCouponIndex.value >= 0) {
    selectedCoupon.value = availableCoupons.value[selectedCouponIndex.value]
  } else {
    selectedCoupon.value = null
  }
  showCouponModal.value = false
}

const handlePayment = () => {
  if (!canPay.value) {
    alert('请选择支付方式')
    return
  }

  // 模拟支付过程
  const paymentMethod = paymentMethods.value[selectedMethodIndex.value]
  console.log('支付信息:', {
    orderId: orderId.value,
    amount: paymentAmount.value,
    method: paymentMethod.name,
    coupon: selectedCoupon.value
  })

  // 模拟支付结果（90%成功率）
  setTimeout(() => {
    paymentSuccess.value = Math.random() > 0.1
    showPaymentResult.value = true
  }, 1500)
}

const closePaymentResult = () => {
  showPaymentResult.value = false
  if (!paymentSuccess.value) {
    // 支付失败，留在当前页面
    return
  }
  // 支付成功，返回首页
  router.push('/')
}

const goToOrders = () => {
  router.push('/orders')
}

onMounted(() => {
  // 获取订单ID
  orderId.value = route.query.orderId || Date.now().toString()
  
  // 从路由参数获取金额信息（实际项目中应该从后端获取）
  if (route.query.amount) {
    originalAmount.value = parseFloat(route.query.amount)
  }
})
</script>

<style scoped>
.payment-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px;
}

/* 头部样式 */
.header {
  background-color: #1c705e;
  color: white;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  height: 56px;
  max-width: 1200px;
  margin: 0 auto;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  margin-right: 16px;
}

.page-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

/* 内容区域 */
.payment-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 金额显示 */
.amount-section {
  background: white;
  border-radius: 12px;
  padding: 30px 20px;
  text-align: center;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.amount-display {
  display: flex;
  justify-content: center;
  align-items: baseline;
  margin-bottom: 8px;
}

.currency {
  font-size: 24px;
  color: #e53935;
  margin-right: 4px;
}

.amount {
  font-size: 36px;
  font-weight: 600;
  color: #e53935;
}

.order-info {
  color: #666;
  margin: 0;
  font-size: 14px;
}

/* 通用区块样式 */
.payment-methods,
.payment-details {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

/* 支付方式样式 */
.method-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.method-item {
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 12px;
}

.method-item.active {
  border-color: #1c705e;
  background-color: #f0f8f5;
}

.method-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.method-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.method-info {
  flex: 1;
}

.method-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #333;
}

.method-desc {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.method-radio {
  margin-left: auto;
}

.radio {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #ddd;
  border-radius: 50%;
  position: relative;
}

.radio.checked {
  border-color: #1c705e;
}

.radio.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  background-color: #1c705e;
  border-radius: 50%;
}

/* 优惠券样式 */
.coupon-section {
  background: white;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.coupon-item {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.coupon-item:hover {
  background-color: #f8f8f8;
}

.coupon-icon {
  font-size: 24px;
}

.coupon-info {
  flex: 1;
}

.coupon-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.coupon-desc {
  display: block;
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.coupon-arrow {
  color: #ccc;
  font-size: 16px;
}

/* 支付详情样式 */
.detail-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.detail-item.total {
  border-top: 1px solid #e5e5e5;
  padding-top: 12px;
  font-weight: 600;
  font-size: 16px;
}

.detail-label {
  color: #666;
}

.detail-value {
  color: #333;
  font-weight: 500;
}

.detail-value.discount {
  color: #e53935;
}

.detail-item.total .detail-value {
  color: #e53935;
  font-size: 18px;
}

/* 底部支付按钮 */
.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px 20px;
  border-top: 1px solid #e5e5e5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
}

.payment-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.total-label {
  font-size: 16px;
  color: #666;
}

.total-amount {
  font-size: 20px;
  font-weight: 600;
  color: #e53935;
}

.pay-btn {
  background-color: #1c705e;
  color: white;
  border: none;
  padding: 12px 32px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.pay-btn:hover {
  background-color: #155a4a;
}

.pay-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.coupon-modal,
.payment-result-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e5e5;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-content {
  padding: 20px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #e5e5e5;
}

.confirm-btn {
  width: 100%;
  padding: 12px;
  background-color: #1c705e;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
}

/* 优惠券卡片样式 */
.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.coupon-card {
  display: flex;
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.coupon-card.active {
  border-color: #1c705e;
}

.coupon-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.coupon-left {
  background: linear-gradient(135deg, #1c705e, #2d8a6e);
  color: white;
  padding: 20px;
  text-align: center;
  min-width: 100px;
}

.coupon-amount {
  font-size: 20px;
  font-weight: 600;
}

.coupon-condition {
  font-size: 12px;
  margin-top: 4px;
}

.coupon-right {
  flex: 1;
  padding: 16px;
  position: relative;
}

.coupon-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #333;
}

.coupon-validity {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.coupon-status {
  position: absolute;
  top: 16px;
  right: 16px;
  background: #f5f5f5;
  color: #999;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.no-coupon-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 2px solid #e5e5e5;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.no-coupon-option.active {
  border-color: #1c705e;
  background-color: #f0f8f5;
}

/* 支付结果样式 */
.payment-result-modal {
  text-align: center;
  padding: 40px 20px;
}

.result-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.result-title {
  margin: 0 0 12px 0;
  font-size: 24px;
  color: #333;
}

.result-message {
  margin: 0 0 30px 0;
  color: #666;
  line-height: 1.5;
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.result-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.result-btn:not(.secondary) {
  background-color: #1c705e;
  color: white;
}

.result-btn.secondary {
  background-color: #f5f5f5;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .payment-content {
    padding: 16px;
  }
  
  .amount-display {
    flex-direction: column;
    align-items: center;
  }
  
  .currency {
    margin-right: 0;
    margin-bottom: 4px;
  }
  
  .result-actions {
    flex-direction: column;
  }
}
</style>
