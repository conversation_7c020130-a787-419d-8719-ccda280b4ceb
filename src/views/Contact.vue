<template>
  <div>
    <Header scroll placeholder></Header>
    <div class="banner-section">
      <div class="banner-container">
        <img src="@/assets/contact/lxwm_banner.png" alt="联系我们" class="banner-img" :class="{ 'mobile-banner': isMobile }">
        <div class="banner-overlay">
          <div class="banner-content">
            <h2 class="banner-title">联系我们</h2>
            <p class="banner-subtitle">CONTACT US</p>
          </div>
        </div>
      </div>
      <div class="banner-button-section">
        <div class="btn banner-btn">联系我们</div>
      </div>
    </div>
    <!-- 联系信息区域 -->
    <div class="contact-section">
      <div class="contact-container">
        <!-- 桌面端布局 -->
        <div v-if="!isMobile" class="contact-content-desktop">
          <div class="contact-info-desktop">
            <div class="content-wrap">
              <h3 class="title">办公地址</h3>
              <div class="info-item" @click="handleContactClick('address', contactInfo.address)">
                <img src="@/assets/contact/h_lx_icon1.png" alt="" class="info-icon">
                <p class="content clickable">{{ contactInfo.address }}</p>
              </div>
            </div>
            <div class="content-wrap">
              <h3 class="title">招商电话</h3>
              <div class="info-item" @click="handleContactClick('phone', contactInfo.phone)">
                <img src="@/assets/contact/h_lx_icon2.png" alt="" class="info-icon">
                <p class="content clickable">加盟热线：{{ contactInfo.phone }}</p>
              </div>
            </div>
            <div class="content-wrap">
              <h3 class="title">邮箱地址</h3>
              <div class="info-item" @click="handleContactClick('email', contactInfo.email)">
                <img src="@/assets/contact/h_lx_icon1.png" alt="" class="info-icon">
                <p class="content clickable">{{ contactInfo.email }}</p>
              </div>
            </div>
            <div class="content-wrap">
              <h3 class="title">门店地址</h3>
              <div class="store-list">
                <p v-for="(store, index) in storeList" :key="index" class="address">{{ store }}</p>
              </div>
            </div>
          </div>
          <div class="contact-map-desktop">
            <img src="@/assets/contact/lxwm_ditu.png" alt="地图" class="map-img">
          </div>
        </div>

        <!-- 移动端布局 -->
        <div v-else class="contact-content-mobile">
          <!-- 快速联系卡片 -->
          <div class="quick-contact-cards">
            <div class="contact-card phone-card" @click="handleContactClick('phone', contactInfo.phone)">
              <div class="card-icon">
                <img src="@/assets/contact/h_lx_icon2.png" alt="电话">
              </div>
              <div class="card-content">
                <h4 class="card-title">立即拨打</h4>
                <p class="card-text">{{ contactInfo.phone }}</p>
              </div>
              <div class="card-arrow">📞</div>
            </div>

            <div class="contact-card email-card" @click="handleContactClick('email', contactInfo.email)">
              <div class="card-icon">
                <img src="@/assets/contact/h_lx_icon1.png" alt="邮箱">
              </div>
              <div class="card-content">
                <h4 class="card-title">发送邮件</h4>
                <p class="card-text">{{ contactInfo.email }}</p>
              </div>
              <div class="card-arrow">✉️</div>
            </div>
          </div>

          <!-- 地址信息 -->
          <div class="mobile-address-section">
            <h3 class="mobile-section-title">办公地址</h3>
            <div class="mobile-address-card" @click="handleContactClick('address', contactInfo.address)">
              <div class="address-icon">📍</div>
              <div class="address-content">
                <p class="address-text">{{ contactInfo.address }}</p>
                <span class="address-action">点击查看地图</span>
              </div>
            </div>
          </div>

          <!-- 地图 -->
          <div class="mobile-map-section">
            <h3 class="mobile-section-title">位置地图</h3>
            <div class="mobile-map-container">
              <img src="@/assets/contact/lxwm_ditu.png" alt="地图" class="mobile-map-img">
            </div>
          </div>

          <!-- 门店列表 -->
          <div class="mobile-stores-section">
            <h3 class="mobile-section-title">门店分布</h3>
            <div class="mobile-stores-grid">
              <div v-for="(store, index) in storeList" :key="index" class="mobile-store-item">
                <div class="store-icon">🏪</div>
                <div class="store-info">
                  <p class="store-name">{{ store }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Footer></Footer>
    <SettleIn/>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import SettleIn from "@/components/SettleIn.vue";

const isMobile = ref(false)
const deviceInfo = ref({
  type: '',
  os: '',
  browser: '',
  screenWidth: 0
})

// 联系信息数据
const contactInfo = ref({
  address: '广西梧州六堡茶股份有限公司的地址为广西壮族自治区梧州市长洲区龙腾路106号第1幢',
  phone: '400-8888-888',
  email: '<EMAIL>'
})

// 门店列表
const storeList = ref([
  '梧州市六堡茶文化馆茶船古道旗舰店',
  '梧州市居仁路茶船古道旗舰店',
  '梧州市龙腾路茶船古道店',
  '南宁市安园东路吉顺茶馆',
  '南宁市凤岭南路1号梧州六堡茶馆',
  '南宁市朱槿路汇贤庄',
  '岑溪市六堡茶店',
  '扬州市瘦西湖东门店',
  '南京市环湖假日店'
])

// 获取设备信息
const getDeviceInfo = () => {
  const userAgent = navigator.userAgent.toLowerCase()
  const screenWidth = window.innerWidth

  // 检测操作系统
  let os = 'Unknown'
  if (userAgent.includes('android')) os = 'Android'
  else if (userAgent.includes('iphone') || userAgent.includes('ipad')) os = 'iOS'
  else if (userAgent.includes('windows')) os = 'Windows'
  else if (userAgent.includes('mac')) os = 'macOS'
  else if (userAgent.includes('linux')) os = 'Linux'

  // 检测浏览器
  let browser = 'Unknown'
  if (userAgent.includes('chrome')) browser = 'Chrome'
  else if (userAgent.includes('firefox')) browser = 'Firefox'
  else if (userAgent.includes('safari')) browser = 'Safari'
  else if (userAgent.includes('edge')) browser = 'Edge'

  // 检测设备类型
  let type = 'Desktop'
  if (userAgent.includes('mobile') || screenWidth <= 768) type = 'Mobile'
  else if (userAgent.includes('tablet') || (screenWidth > 768 && screenWidth <= 1024)) type = 'Tablet'

  deviceInfo.value = { type, os, browser, screenWidth }

  return { type, os, browser, screenWidth }
}

// 检测是否为移动设备
const checkMobile = () => {
  const { type, screenWidth } = getDeviceInfo()
  const userAgent = navigator.userAgent.toLowerCase()

  // 更精确的移动设备检测
  const mobileKeywords = ['mobile', 'android', 'iphone', 'ipod', 'blackberry', 'windows phone']
  const tabletKeywords = ['ipad', 'tablet']

  const isMobileDevice = mobileKeywords.some(keyword => userAgent.includes(keyword))
  const isTabletDevice = tabletKeywords.some(keyword => userAgent.includes(keyword))
  const isSmallScreen = screenWidth <= 768
  const isMediumScreen = screenWidth > 768 && screenWidth <= 1024

  // 移动设备或小屏幕显示移动端界面
  isMobile.value = isMobileDevice || isSmallScreen || (isTabletDevice && isMediumScreen)
}

// 监听窗口大小变化
const handleResize = () => {
  checkMobile()
}

// 处理联系方式点击
const handleContactClick = (type, value) => {
  switch (type) {
    case 'phone':
      window.open(`tel:${value}`)
      break
    case 'email':
      window.open(`mailto:${value}`)
      break
    case 'address':
      // 可以打开地图应用
      const encodedAddress = encodeURIComponent(value)
      window.open(`https://maps.google.com/maps?q=${encodedAddress}`)
      break
    default:
      break
  }
}

// 组件挂载时初始化
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 基础样式 */
.btn {
  background-color: #1f7260;
  border: 1px solid white;
  padding: 10px 30px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.btn:hover {
  background-color: #155a4a;
  transform: translateY(-2px);
}

/* 横幅区域样式 */
.banner-section {
  position: relative;
}

.banner-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.banner-img {
  width: 100%;
  height: 350px;
  object-fit: cover;
}

.mobile-banner {
  height: 250px;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
}

.banner-content {
  text-align: center;
  color: white;
}

.banner-title {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-subtitle {
  font-size: 1.2rem;
  margin: 10px 0;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.banner-button-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
}

.banner-btn {
  font-size: 1rem;
  padding: 12px 40px;
}

/* 联系信息区域样式 */
.contact-section {
  background: url("@/assets/contact/lxwm_bj.png");
  background-size: cover;
  background-position: center;
  padding: 2rem 2rem 10rem 2rem;
  min-height: 100vh;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 桌面端联系信息样式 */
.contact-content-desktop {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

.contact-info-desktop {
  flex: 1;
  border-radius: 25px;
  height: 712px;
  background: linear-gradient(180deg, #ddf5ef 0%, white 100%);
  box-shadow: 0 10px 10px 10px rgba(0, 0, 0, 0.1);
}

.contact-map-desktop {
  flex: 1;
  border-radius: 25px;
  overflow: hidden;
  box-shadow: 0 10px 10px 10px rgba(0, 0, 0, 0.1);
}

.map-img {
  width: 100%;
  height: auto;
}

.content-wrap {
  margin: 2rem;
}

.title {
  color: #006c55;
  margin-bottom: 15px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 8px;
}

.info-item:hover {
  background: rgba(31, 114, 96, 0.1);
}

.info-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
}

.content {
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

.content.clickable {
  color: #1f7260;
  transition: color 0.3s ease;
}

.content.clickable:hover {
  color: #155a4a;
}

.store-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.address {
  margin: 0;
  font-size: 0.8rem;
  color: #999;
  line-height: 1.4;
}

/* 移动端联系信息样式 */
.contact-content-mobile {
  display: flex;
  flex-direction: column;
  gap: 25px;
  padding: 0 15px;
}

/* 快速联系卡片 */
.quick-contact-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 10px;
}

.contact-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.phone-card {
  border-color: rgba(31, 114, 96, 0.2);
}

.phone-card:hover {
  border-color: #1f7260;
  background: rgba(31, 114, 96, 0.05);
}

.email-card {
  border-color: rgba(52, 152, 219, 0.2);
}

.email-card:hover {
  border-color: #3498db;
  background: rgba(52, 152, 219, 0.05);
}

.card-icon {
  margin-bottom: 10px;
}

.card-icon img {
  width: 24px;
  height: 24px;
}

.card-content {
  flex: 1;
}

.card-title {
  color: #333;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 5px 0;
}

.card-text {
  color: #666;
  font-size: 12px;
  margin: 0;
  line-height: 1.3;
}

.card-arrow {
  font-size: 18px;
  margin-top: 8px;
}

/* 移动端地址区域 */
.mobile-address-section,
.mobile-map-section,
.mobile-stores-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.mobile-section-title {
  color: #1f7260;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(31, 114, 96, 0.2);
}

.mobile-address-card {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 15px;
  border-radius: 12px;
  border: 2px solid rgba(31, 114, 96, 0.1);
}

.mobile-address-card:hover {
  background: rgba(31, 114, 96, 0.05);
  border-color: #1f7260;
}

.address-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.address-content {
  flex: 1;
}

.address-text {
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 8px 0;
}

.address-action {
  color: #1f7260;
  font-size: 12px;
  font-weight: 500;
}

/* 移动端地图 */
.mobile-map-container {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.mobile-map-img {
  width: 100%;
  height: auto;
  display: block;
}

/* 移动端门店列表 */
.mobile-stores-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.mobile-store-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(31, 114, 96, 0.1);
  transition: all 0.3s ease;
}

.mobile-store-item:hover {
  background: rgba(31, 114, 96, 0.05);
  border-color: #1f7260;
}

.store-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.store-info {
  flex: 1;
}

.store-name {
  color: #333;
  font-size: 13px;
  line-height: 1.4;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-title {
    font-size: 2rem;
  }

  .banner-subtitle {
    font-size: 1rem;
    margin: 8px 0;
  }

  .banner-btn {
    padding: 10px 30px;
    font-size: 0.9rem;
  }

  .contact-section {
    padding: 20px 0 60px 0;
  }

  .contact-content-mobile {
    padding: 0 10px;
    gap: 20px;
  }

  .quick-contact-cards {
    gap: 12px;
  }

  .contact-card {
    padding: 15px 10px;
  }

  .card-title {
    font-size: 13px;
  }

  .card-text {
    font-size: 11px;
  }

  .mobile-section-title {
    font-size: 1.1rem;
  }

  .mobile-address-card {
    padding: 12px;
  }

  .address-text {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .banner-img {
    height: 200px;
  }

  .banner-title {
    font-size: 1.8rem;
  }

  .banner-subtitle {
    font-size: 0.9rem;
  }

  .banner-btn {
    padding: 8px 20px;
    font-size: 0.8rem;
  }

  .contact-section {
    padding: 15px 0 40px 0;
  }

  .contact-content-mobile {
    padding: 0 8px;
    gap: 15px;
  }

  .quick-contact-cards {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .contact-card {
    padding: 12px 8px;
  }

  .mobile-address-section,
  .mobile-map-section,
  .mobile-stores-section {
    padding: 15px;
  }

  .mobile-section-title {
    font-size: 1rem;
    margin-bottom: 12px;
  }

  .mobile-address-card {
    padding: 10px;
  }

  .address-text {
    font-size: 12px;
  }

  .store-name {
    font-size: 12px;
  }
}
</style>