<template>
  <div class="category-page">
    <Header/>
    <!-- 顶部横幅 -->
    <div class="banner">
      <img src="@/assets/images/category/cate-banner.png" alt="茶艺展示" class="banner-image"/>
    </div>

    <!-- 产品系列标题 -->
    <div class="section-title container">
      <h2>产品系列</h2>
      <div class="en-title">PRODUCT SERIES</div>
      <div class="title-line"></div>
    </div>

    <!-- 产品列表 -->
    <div class="product-list container">
      <div class="sidebar">
        <H4 style="margin: 0; padding: 0">三原系列</H4>
        <ul class="category-menu">
          <li class="active">特色系列</li>
          <li>人文旅行系列</li>
          <li>传统节庆系列</li>
          <li>客服定制系列</li>
          <li>经典系列</li>
          <li>福禄寿喜伴手礼系列</li>
          <li>茶船古道2025高端茶礼系列</li>
          <li>军创定制系列</li>
          <li>六堡茶散茶系列</li>
        </ul>
      </div>

      <div class="product-grid">
        <div class="product-item" v-for="(product, index) in products" :key="index" @click="toDetail">
          <div class="product-image">
            <img :src="product.image" :alt="product.title"/>
          </div>
          <div class="product-info">
            <div class="product-title">{{ product.title }}</div>
            <div class="product-meta">
              <span class="product-sales">规格: {{ product.sku }}g/盒</span>
              <span class="product-price">¥{{ product.price }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧工具栏 -->
      <div class="side-tools">
        <div class="tool-item">
          <i class="el-icon-service"></i>
        </div>
        <div class="tool-item">
          <i class="el-icon-shopping-cart-full"></i>
        </div>
        <div class="tool-item">
          <i class="el-icon-star-off"></i>
        </div>
        <div class="tool-item">
          <i class="el-icon-top"></i>
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <Footer/>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Footer from "@/components/Footer.vue";
import Header from "@/components/Header.vue";

import productImage0 from '@/assets/images/products/sy_img1.png'
import productImage1 from '@/assets/images/products/sy_img2.png'
import productImage2 from '@/assets/images/products/sy_img3.png'
import productImage3 from '@/assets/images/products/sy_img4.png'
import productImage4 from '@/assets/images/products/sy_img5.png'

import productImage01 from '@/assets/images/products/ts_img1.png'
import productImage11 from '@/assets/images/products/ts_img2.png'
import productImage21 from '@/assets/images/products/ts_img3.png'
import productImage31 from '@/assets/images/products/ts_img4.png'

// 产品数据
const products = ref([
  {
    title: '武夷山大红袍',
    price: '298',
    sku: '500',
    image: productImage0
  },
  {
    title: '明前西湖龙井',
    price: '368',
    sku: '500',
    image: productImage1
  },
  {
    title: '安溪铁观音',
    price: '258',
    sku: '500',
    image: productImage2
  },
  {
    title: '武夷岩茶',
    price: '328',
    sku: '500',
    image: productImage3
  },
  {
    title: '普洱生茶',
    price: '398',
    sku: '500',
    image: productImage4
  },
  {
    title: '普洱熟茶',
    price: '428',
    sku: '500',
    image: productImage01
  },
  {
    title: '金骏眉红茶',
    price: '268',
    sku: '500',
    image: productImage11
  },
  {
    title: '茉莉花茶',
    price: '198',
    sku: '500',
    image: productImage21
  },
  {
    title: '碧螺春',
    price: '288',
    sku: '500',
    image: productImage31
  }
])

const toDetail = () => {
  this.$router.push({
    path: '/detail'
  })
}
</script>

<style scoped>
.category-page {
  width: 1200px;
  margin: auto;
}

/* 横幅样式 */
.banner {
  width: 100%;
  height: 600px;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 产品系列标题 */
.section-title {
  text-align: left;
  margin: 30px auto;
  position: relative;
}

.section-title h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 5px;
}

.en-title {
  font-size: 14px;
  color: #999;
  text-transform: uppercase;
}

.title-line {
  width: 100%;
  height: 1px;
  background-color: #eee;
  margin-top: 10px;
}

/* 产品列表区域 */
.product-list {
  display: flex;
  margin: 30px auto;
  position: relative;
}

/* 侧边栏 */
.sidebar {
  width: 200px;
  margin-right: 30px;
}

.category-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-menu li {
  padding: 12px 0;
  cursor: pointer;
  transition: all 0.3s;
  color: #999;
}

.category-menu li:last-child {
  border-bottom: none;
}

.category-menu li:hover,
.category-menu li.active {
  color: #1e8e3e;
  background-color: #f9f9f9;
}

/* 产品网格 */
.product-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.product-item {
  border-radius: 5px;
  overflow: hidden;
  transition: all 0.3s;
}

.product-item:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-5px);
}

.product-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
}

.product-item:hover .product-image img {
  transform: scale(1.05);
}

.product-info {
  padding: 15px;
}

.product-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  color: #666;
  font-size: 14px;
}

.product-sales {
  color: #999;
  font-size: 12px;
}

/* 右侧工具栏 */
.side-tools {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  z-index: 100;
}

.tool-item {
  width: 40px;
  height: 40px;
  background-color: #fff;
  border: 1px solid #eee;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 5px;
  cursor: pointer;
  transition: all 0.3s;
}

.tool-item:hover {
  background-color: #1e8e3e;
  color: white;
}

.tool-item i {
  font-size: 20px;
}


.footer-sections {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  margin-bottom: 30px;
}

.footer-section h4 {
  font-size: 16px;
  color: #333;
  margin-bottom: 15px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
}

.footer-section li:hover {
  color: #1e8e3e;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.footer-links {
  display: flex;
}

.footer-link {
  display: flex;
  align-items: center;
  margin-right: 20px;
  color: #666;
  text-decoration: none;
}

.footer-link i {
  margin-right: 5px;
}

.qrcode {
  text-align: center;
}

.company-info {
  display: flex;
  align-items: center;
}

.company-logo {
  margin-right: 15px;
}

.social-icons {
  display: flex;
  gap: 10px;
  margin-top: 5px;
}

.social-icons img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.copyright {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin-top: 20px;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .footer-sections {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .product-list {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    margin-right: 0;
    margin-bottom: 20px;
  }
  
  .category-menu {
    display: flex;
    flex-wrap: wrap;
  }
  
  .category-menu li {
    flex: 1 0 auto;
    text-align: center;
    border-right: 1px solid #eee;
    border-bottom: 1px solid #eee;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .product-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-sections {
    grid-template-columns: 1fr;
  }
}
</style>