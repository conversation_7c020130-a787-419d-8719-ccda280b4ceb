<template>
  <div class="balance-page">
    <Header :scroll="scroll"/>
    
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="container">
        <h1 class="page-title">余额明细</h1>
        <p class="page-subtitle">查看您的账户余额和交易记录</p>
      </div>
    </div>

    <!-- 余额概览区域 -->
    <div class="balance-overview">
      <div class="container">
        <div class="balance-cards">
          <div class="balance-card main-balance">
            <div class="balance-icon">
              <span class="icon">💰</span>
            </div>
            <div class="balance-info">
              <h3 class="balance-title">账户余额</h3>
              <div class="balance-amount">
                <span class="currency">¥</span>
                <span class="amount">{{ userBalance.totalBalance }}</span>
              </div>
            </div>
          </div>
          
          <div class="balance-card">
            <div class="balance-icon">
              <span class="icon">🎁</span>
            </div>
            <div class="balance-info">
              <h3 class="balance-title">赠送余额</h3>
              <div class="balance-amount">
                <span class="currency">¥</span>
                <span class="amount">{{ userBalance.giftBalance }}</span>
              </div>
            </div>
          </div>

          <div class="balance-card">
            <div class="balance-icon">
              <span class="icon">⭐</span>
            </div>
            <div class="balance-info">
              <h3 class="balance-title">积分余额</h3>
              <div class="balance-amount">
                <span class="amount">{{ userBalance.points }}</span>
                <span class="unit">分</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="quick-actions">
          <button class="action-btn recharge-btn" @click="recharge">
            <span class="btn-icon">💳</span>
            充值
          </button>
          <button class="action-btn withdraw-btn" @click="withdraw">
            <span class="btn-icon">🏦</span>
            提现
          </button>
          <button class="action-btn transfer-btn" @click="transfer">
            <span class="btn-icon">💸</span>
            转账
          </button>
        </div>
      </div>
    </div>

    <!-- 交易记录筛选 -->
    <div class="transaction-filter">
      <div class="container">
        <div class="filter-tabs">
          <div 
            class="filter-tab" 
            :class="{ active: activeTab === 'all' }" 
            @click="setActiveTab('all')"
          >
            全部记录
          </div>
          <div 
            class="filter-tab" 
            :class="{ active: activeTab === 'income' }" 
            @click="setActiveTab('income')"
          >
            收入
          </div>
          <div 
            class="filter-tab" 
            :class="{ active: activeTab === 'expense' }" 
            @click="setActiveTab('expense')"
          >
            支出
          </div>
          <div 
            class="filter-tab" 
            :class="{ active: activeTab === 'recharge' }" 
            @click="setActiveTab('recharge')"
          >
            充值
          </div>
          <div 
            class="filter-tab" 
            :class="{ active: activeTab === 'withdraw' }" 
            @click="setActiveTab('withdraw')"
          >
            提现
          </div>
        </div>
        
        <!-- 时间筛选 -->
        <div class="date-filter">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="filterByDate"
          />
        </div>
      </div>
    </div>

    <!-- 交易记录列表 -->
    <div class="transaction-content">
      <div class="container">
        <div class="transaction-list">
          <div 
            v-for="(transaction, index) in filteredTransactions" 
            :key="transaction.id" 
            class="transaction-item"
            :class="transaction.type"
          >
            <div class="transaction-icon">
              <span class="type-icon">{{ getTransactionIcon(transaction.type) }}</span>
            </div>
            <div class="transaction-info">
              <h4 class="transaction-title">{{ transaction.title }}</h4>
              <p class="transaction-desc">{{ transaction.description }}</p>
              <span class="transaction-time">{{ transaction.createTime }}</span>
            </div>
            <div class="transaction-amount">
              <span class="amount" :class="{ 'positive': transaction.type === 'income' || transaction.type === 'recharge', 'negative': transaction.type === 'expense' || transaction.type === 'withdraw' }">
                {{ transaction.type === 'income' || transaction.type === 'recharge' ? '+' : '-' }}¥{{ Math.abs(transaction.amount) }}
              </span>
              <span class="status" :class="transaction.status">{{ getStatusText(transaction.status) }}</span>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredTransactions.length === 0" class="empty-state">
          <div class="empty-icon">
            <img src="@/assets/images/cart-green.png" alt="暂无记录" class="empty-image">
          </div>
          <p class="empty-text">暂无交易记录</p>
          <p class="empty-desc">您还没有任何交易记录</p>
        </div>

        <!-- 分页 -->
        <div v-if="filteredTransactions.length > 0" class="pagination">
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="totalTransactions"
            layout="prev, pager, next"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 页脚 -->
    <Footer/>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'

const router = useRouter()
const scroll = ref(false)

// 监听滚动
window.addEventListener('scroll', () => {
  scroll.value = window.scrollY > 0
})

// 当前激活的标签
const activeTab = ref('all')

// 日期筛选
const dateRange = ref([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalTransactions = ref(0)

// 用户余额信息
const userBalance = ref({
  totalBalance: 1580.50,
  giftBalance: 120.00,
  points: 2580
})

// 交易记录数据
const transactions = ref([
  {
    id: 1,
    title: '购买商品',
    description: '茶船古道·祝雪兰云雾六堡茶·春岚韵',
    amount: -58.00,
    type: 'expense',
    status: 'completed',
    createTime: '2024-12-25 14:30:25'
  },
  {
    id: 2,
    title: '账户充值',
    description: '微信支付充值',
    amount: 200.00,
    type: 'recharge',
    status: 'completed',
    createTime: '2024-12-24 16:45:12'
  },
  {
    id: 3,
    title: '退款到账',
    description: '订单退款',
    amount: 128.00,
    type: 'income',
    status: 'completed',
    createTime: '2024-12-23 10:20:08'
  },
  {
    id: 4,
    title: '提现申请',
    description: '提现到银行卡',
    amount: -100.00,
    type: 'withdraw',
    status: 'pending',
    createTime: '2024-12-22 09:15:33'
  },
  {
    id: 5,
    title: '签到奖励',
    description: '连续签到7天奖励',
    amount: 10.00,
    type: 'income',
    status: 'completed',
    createTime: '2024-12-21 08:00:00'
  },
  {
    id: 6,
    title: '购买商品',
    description: '非遗六堡茶·乡村振兴版',
    amount: -128.00,
    type: 'expense',
    status: 'completed',
    createTime: '2024-12-20 15:30:45'
  }
])

// 筛选后的交易记录
const filteredTransactions = computed(() => {
  let filtered = transactions.value

  // 按类型筛选
  if (activeTab.value !== 'all') {
    filtered = filtered.filter(transaction => transaction.type === activeTab.value)
  }

  // 按日期筛选
  if (dateRange.value && dateRange.value.length === 2) {
    const startDate = new Date(dateRange.value[0])
    const endDate = new Date(dateRange.value[1])
    filtered = filtered.filter(transaction => {
      const transactionDate = new Date(transaction.createTime)
      return transactionDate >= startDate && transactionDate <= endDate
    })
  }

  return filtered
})

// 设置激活标签
const setActiveTab = (tab) => {
  activeTab.value = tab
  currentPage.value = 1
}

// 日期筛选
const filterByDate = () => {
  currentPage.value = 1
}

// 获取交易图标
const getTransactionIcon = (type) => {
  const iconMap = {
    'income': '💰',
    'expense': '🛒',
    'recharge': '💳',
    'withdraw': '🏦'
  }
  return iconMap[type] || '💰'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'completed': '已完成',
    'pending': '处理中',
    'failed': '失败'
  }
  return statusMap[status] || '未知'
}

// 充值
const recharge = () => {
  ElMessage.info('充值功能开发中...')
}

// 提现
const withdraw = () => {
  ElMessage.info('提现功能开发中...')
}

// 转账
const transfer = () => {
  ElMessage.info('转账功能开发中...')
}

// 分页处理
const handlePageChange = (page) => {
  currentPage.value = page
  // 这里可以添加API调用获取对应页面的数据
}

// 初始化
onMounted(() => {
  totalTransactions.value = transactions.value.length
})
</script>
